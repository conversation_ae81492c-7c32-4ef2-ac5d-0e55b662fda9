import React from "react";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, FileSpreadsheet, FileText } from "lucide-react";
import { useCsvDownload } from "@/hooks/useCsvDownload";

interface CsvDownloadDropdownProps {
	className?: string;
}

export const CsvDownloadDropdown: React.FC<CsvDownloadDropdownProps> = ({
	className,
}) => {
	const { downloadSampleCsv, exportClients, isDownloading } =
		useCsvDownload();

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="outline"
					size="icon"
					className={`h-9 w-9 ${className}`}
					disabled={isDownloading}
				>
					<Download className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuItem
					onClick={downloadSampleCsv}
					disabled={isDownloading}
				>
					<FileText className="mr-2 h-4 w-4" />
					{isDownloading ? "Downloading..." : "Download Sample CSV"} 
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={exportClients}
					disabled={isDownloading}
				>
					<FileSpreadsheet className="mr-2 h-4 w-4" />
					{isDownloading ? "Exporting..." : "Export Clients"}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
