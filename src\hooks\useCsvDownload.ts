import { useState } from "react";
import { csvDownloadApi } from "@/lib/api/csvDownloadApi";
import { useUIStore } from "@/stores/uiStore";

export const useCsvDownload = () => {
	const [isDownloading, setIsDownloading] = useState(false);
	const { addToast } = useUIStore();

	const downloadSampleCsv = async () => {
		setIsDownloading(true);
		try {
			const blob = await csvDownloadApi.downloadSampleCsv();

			// Create download link
			const url = window.URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.href = url;
			link.download = "client_import_sample.csv";

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Cleanup
			window.URL.revokeObjectURL(url);

			addToast({
				type: "success",
				title: "Download Successful",
				message: "Sample CSV file downloaded successfully",
			});
		} catch (error) {
			console.error("Download failed:", error);
			addToast({
				type: "error",
				title: "Download Failed",
				message: "Failed to download sample CSV file",
			});
		} finally {
			setIsDownloading(false);
		}
	};

	const exportClients = async () => {
		setIsDownloading(true);
		try {
			const blob = await csvDownloadApi.exportClients();

			// Create download link
			const url = window.URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.href = url;
			link.download = "clients_export.csv";

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Cleanup
			window.URL.revokeObjectURL(url);

			addToast({
				type: "success",
				title: "Export Successful",
				message: "Client data exported successfully",
			});
		} catch (error) {
			console.error("Export failed:", error);
			addToast({
				type: "error",
				title: "Export Failed",
				message: "Failed to export client data",
			});
		} finally {
			setIsDownloading(false);
		}
	};

	return {
		downloadSampleCsv,
		exportClients,
		isDownloading,
	};
};
