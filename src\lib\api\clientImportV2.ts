import { apiClient } from "./clients";
import type {
	SystemField,
	ValidationResponse,
	AddAttributeRequest,
	DataValidationRequest,
	DataValidationResponse,
	CommitDataResponse,
} from "@/pages/ManageClients/ClientImportV2/types";

export interface GetAttributesResponse {
	success: boolean;
	data: SystemField[];
	message: string;
}

export interface ValidateAttributeMappingRequest {
	sheetId: string;
	headers: Array<{
		tempId: string;
		labelOnFile: string | null;
		systemFieldKey: string | null;
		is_validator?: boolean;
	}>;
}

export const clientImportV2Api = {
	getAttributes: async (
		organizationId: number
	): Promise<GetAttributesResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.get(
			"/api/v1/client-import-v2/attributes",
			{ headers }
		);
		return response.data;
	},

	validateAttributeMapping: async (
		data: ValidateAttributeMappingRequest,
		organizationId: number
	): Promise<ValidationResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			"/api/v1/client-import-v2/validate-attribute-mapping",
			data,
			{ headers }
		);
		return response.data;
	},

	validateData: async (
		data: DataValidationRequest,
		organizationId: number
	): Promise<DataValidationResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			"/api/v1/client-import-v2/validate-data",
			data,
			{ headers }
		);
		return response.data;
	},

	commitData: async (
		data: DataValidationRequest,
		organizationId: number
	): Promise<CommitDataResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			"/api/v1/client-import-v2/commit-data",
			data,
			{ headers }
		);
		return response.data;
	},

	addAttribute: async (
		data: AddAttributeRequest,
		organizationId: number
	): Promise<any> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			"/api/v1/client-attributes",
			data,
			{ headers }
		);
		return response.data;
	},
};
