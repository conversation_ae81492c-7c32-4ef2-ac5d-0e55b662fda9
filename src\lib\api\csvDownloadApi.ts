import { apiClient } from "./clients";

export const csvDownloadApi = {
	/**
	 * Download sample CSV for client import
	 */
	downloadSampleCsv: async (): Promise<Blob> => {
		const response = await apiClient.get(
			"/api/v1/client-import/download-sample-csv",
			{
				responseType: "blob",
			}
		);
		return response.data;
	},

	/**
	 * Export all clients to CSV
	 */
	exportClients: async (): Promise<Blob> => {
		const response = await apiClient.get("/api/v1/client-import/export-clients", {
			responseType: "blob",
		});
		return response.data;
	},
};
