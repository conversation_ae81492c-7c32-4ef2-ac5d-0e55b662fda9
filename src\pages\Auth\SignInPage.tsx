import React, { useState } from "react";
import TenantSpecificLoginForm from "../../components/forms/auth/TenantSpecificLoginForm";
import type { TenantConfig } from "../../types/tenant";

const defaultTenantConfig: TenantConfig = {
	id: "default",
	name: "Migranium",
	subdomain: "",
	logo: null,
	authMethods: [
		{ type: "password" },
		{ type: "google" },
		{ type: "microsoft" },
	],
	theme: {
		primaryColor: "#195388",
		secondaryColor: "#858C95",
		backgroundColor: "#FAFBFC",
		textColor: "#323539",
	},
	loginType: "custom",
	showPasswordLogin: true,
	showGoogleLogin: true,
	showMicrosoftLogin: true,
	showSSOLogin: false,
	ssoButtonText: "SSO Login",
};

export const SignInPage: React.FC = () => {
	const [tenantConfig] = useState<TenantConfig>(defaultTenantConfig);
	const handleSwitchToStandard = () => {
		window.open("http://localhost:5173/sign-in", "_blank");
	};

	return (
		<div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
			<div className="w-full max-w-xl space-y-8">
				<TenantSpecificLoginForm
					tenantConfig={tenantConfig}
					onSwitchToStandard={handleSwitchToStandard}
				/>
			</div>
		</div>
	);
};
