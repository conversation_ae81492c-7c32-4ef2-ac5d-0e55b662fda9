import { useMemo, useState, useLayoutEffect } from "react";
import AnalyticsIcon from "@/assets/icons/dashboard/analytics";
import AutomationIcon from "@/assets/icons/dashboard/automation";
import ClientsIcon from "@/assets/icons/dashboard/clients";
import DisplayIcon from "@/assets/icons/dashboard/display";
import FormsIcon from "@/assets/icons/dashboard/forms";
import IntegrationsIcon from "@/assets/icons/dashboard/integrations";
import InventoryIcon from "@/assets/icons/dashboard/inventory";
import KemiIcon from "@/assets/icons/dashboard/kemi";
import MessagingIcon from "@/assets/icons/dashboard/messaging";
import PlannerIcon from "@/assets/icons/dashboard/planner";
import ReferralsIcon from "@/assets/icons/dashboard/referrals";
import ScheduleIcon from "@/assets/icons/dashboard/schedule";
import SettingsIcon from "@/assets/icons/dashboard/settings";
import SpacesIcon from "@/assets/icons/dashboard/spaces";
import TeammembersIcon from "@/assets/icons/dashboard/teammembers";
import VirtualWaitlistIcon from "@/assets/icons/dashboard/virtualWatlist";
import WorkplaceIcon from "@/assets/icons/dashboard/workplace";
import { Button } from "@/components/ui/button";
import {
	// Tooltip,
	// TooltipContent,
	TooltipProvider,
	// TooltipTrigger,
} from "@/components/ui/tooltip";
// import useHeapAnalyticsIdentify from "@/hooks/useHeapAnalyticsIdentify";
// import Header from "@/layouts/Header/Header";
import { usePermissionStore } from "@/stores/permissionStore";
import { SPACES_ENVIRONMENT_LINK } from "@/lib/utils/constants";
import { getCookie } from "@/lib/utils/cookies";
import { cn } from "@/lib/utils";
import {
	DndContext,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";

import type { DragEndEvent } from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	rectSortingStrategy,
	useSortable,
} from "@dnd-kit/sortable";
import { useNavigate } from "react-router";
import { useUIStore } from "@/stores/uiStore";
import { useEffect } from "react";
// import { NavLink } from "react-router-dom";

const Dashboard: React.FC = () => {
	const [items, setItems] = useState<DashboardItem[]>([]);
	const role = usePermissionStore((s) => s.role);
	const selectedOrganisation = usePermissionStore((s) => s.organizationId);
	const features = usePermissionStore((s) => s.features);
	const userRole = role || "TEAM_MEMBER";

	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);

	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	useEffect(() => {
		setCurrentPageTitle("Dashboard");
		return () => setCurrentPageTitle(""); // optional cleanup
	}, [setCurrentPageTitle]);

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Dashboard",
				isCurrentPage: true,
			},
		]);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	// Directly compute items without useState to avoid effect loops
	useLayoutEffect(() => {
		const userProducts = features || [];

		const processedItems = dashboardItems.map((item) => ({
			...item,
			disabled: Boolean(
				item.disabled ||
					(item.requiredProduct &&
						!userProducts.includes(item.requiredProduct))
			),
		}));

		const savedOrder = localStorage.getItem("dashboardItemsOrder");

		if (savedOrder) {
			const orderArray = JSON.parse(savedOrder) as string[];

			if (orderArray.length === processedItems.length) {
				const orderedItems = orderArray
					.map((id) => processedItems.find((item) => item.id === id))
					.filter(
						(item): item is DashboardItem => item !== undefined
					);

				if (orderedItems.length === processedItems.length) {
					setItems(orderedItems);
					return;
				}
			}
		}

		setItems(processedItems);
		localStorage.setItem(
			"dashboardItemsOrder",
			JSON.stringify(processedItems)
		);
	}, [selectedOrganisation]);

	//todo: fix this to get the user selected business, so i cases of multiple business, they wouldnt have access
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 5,
			},
		}),
		useSensor(KeyboardSensor)
	);

	const getSpacesLink = () => {
		const token = getCookie("ac-token");
		const organizationId = getCookie("organisation-id");

		if (!token || !organizationId) {
			console.error("Missing credentials linking to spaces");
			return null;
		}

		return `${SPACES_ENVIRONMENT_LINK}/login?token=${token}&type=admin&organization=${organizationId}`;
	};

	const handleSpacesNavigation = () => {
		const spacesLink = getSpacesLink();
		if (spacesLink) {
			window.open(spacesLink, "_self");
		} else {
			console.error("missing credentials from wrong path");
		}
	};

	// For now, let's disable drag and drop to prevent complications
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			setItems((currentItems) => {
				const oldIndex = currentItems.findIndex(
					(item) => item.id === active.id
				);
				const newIndex = currentItems.findIndex(
					(item) => item.id === over.id
				);

				const newItems = arrayMove(currentItems, oldIndex, newIndex);

				// Save only the IDs order to localStorage
				const newOrder = newItems.map((item) => item.id);
				localStorage.setItem(
					"dashboardItemsOrder",
					JSON.stringify(newOrder)
				);

				return newItems;
			});
		}
	};
	// useHeapAnalyticsIdentify();

	return (
		<section className="flex h-full flex-1">
			<TooltipProvider>
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragEnd={handleDragEnd}
				>
					<section className="grid flex-1 grid-cols-[auto_auto_auto_auto_auto_auto] place-content-center gap-8">
						<SortableContext
							items={items.map((item) => item.id)} // Use id instead of label
							strategy={rectSortingStrategy}
						>
							{items
								.filter((item) =>
									item?.roles?.includes(
										userRole.toUpperCase()
									)
								)
								.map((item) => (
									<SortableDashboardIcon
										key={item.id}
										{...item}
										onSpacesNavigation={
											handleSpacesNavigation
										}
									/>
								))}
						</SortableContext>
					</section>
				</DndContext>
			</TooltipProvider>
		</section>
	);
};

export default Dashboard;

interface SortableDashboardIconProps extends DashboardItem {
	onSpacesNavigation: () => void;
}

const SortableDashboardIcon = ({
	onSpacesNavigation,
	...item
}: SortableDashboardIconProps) => {
	const navigate = useNavigate();
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: item.id,
		disabled: item.disabled,
	});

	const style = {
		transform: transform
			? `translate3d(${transform.x}px, ${transform.y}px, 0)`
			: undefined,
		transition,
	};

	const handleClick = () => {
		if (item.disabled) return;
		if (item.isSpacesLink) {
			onSpacesNavigation();
		} else {
			navigate(item.path ?? "");
		}
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className="px-2 py-[0.78125rem]"
			{...(item.disabled ? {} : attributes)}
			{...(item.disabled ? {} : listeners)}
		>
			<Button
				className={cn(
					"group relative flex h-fit flex-col items-center justify-center gap-y-2 bg-transparent p-0",
					"transform transition-transform duration-200 ease-in-out hover:scale-110 hover:bg-transparent [&_svg:not([class*='size-'])]:size-full",
					{
						"cursor-move": isDragging,
						"cursor-pointer": !isDragging,
						"cursor-default hover:scale-100": item.disabled,
					}
				)}
				onClick={handleClick}
			>
				<figure
					className={cn(
						"rounded-xl p-6 shadow-[0px_0px_8px_0px_rgba(56,74,87,0.24)]",
						{
							"bg-[#EBEBEB] opacity-40": item.disabled,
						}
					)}
				>
					{item.icon}
				</figure>
				<p className="text-sm font-medium text-[#596574]">
					{item.label}
				</p>
				<div
					className={cn(
						"absolute top-0 -mt-2 hidden h-[1.125rem] transform place-content-center rounded-full bg-[#059669] px-1 !text-[10px] leading-[0.6rem] text-white duration-200 ease-in-out",
						{
							"group-hover:grid": item.disabled,
						}
					)}
				>
					{item.disabledMessage || "Coming Soon"}
				</div>
				{/* <div
					className={cn(
						"absolute top-0 -mt-2 hidden h-[1.125rem] transform place-content-center rounded-full bg-[#059669] px-1 !text-[10px] leading-[0.6rem] text-white duration-200 ease-in-out",
						{
							"group-hover:grid": item.disabled,
						}
					)}
				>
					Coming Soon
				</div> */}
			</Button>
		</div>
	);
};

interface DashboardItem {
	id: string; // Add ID field
	icon: React.ReactNode;
	label: string;
	path?: string;
	disabled: boolean;
	isSpacesLink?: boolean;
	requiredProduct?: "primary" | "room_booking" | "spaces";
	disabledMessage?: string;
	roles: string[];
}

const dashboardItems: DashboardItem[] = [
	{
		id: "scheduler",
		icon: <ScheduleIcon />,
		label: "Scheduler",
		path: "/dashboard/schedule/manage-appointments",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "virtual-waitlist",
		icon: <VirtualWaitlistIcon />,
		label: "Virtual Waitlist",
		path: "/dashboard/waitlist",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "planner",
		icon: <PlannerIcon />,
		label: "Planner",
		path: "/dashboard/schedule/planner",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "analytics",
		icon: <AnalyticsIcon />,
		label: "Analytics",
		path: "/dashboard/analytics",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},

	{
		id: "display",
		icon: <DisplayIcon />,
		label: "Display",
		path: "/dashboard/display",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "forms",
		icon: <FormsIcon />,
		label: "Forms",
		path: "/dashboard/forms",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "workplace",
		icon: <WorkplaceIcon />,
		label: "Workplace",
		path: "/dashboard/workplace/locations",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "team-members",
		icon: <TeammembersIcon />,
		label: "Team Members",
		path: "/dashboard/workplace/locations/?location-tab=team-members",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "patients",
		icon: <ClientsIcon />,
		label: "Patients",
		path: "/dashboard/patients",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "spaces",
		icon: <SpacesIcon />,
		label: "Spaces",
		path: "/dashboard/spaces",
		disabled: false,
		isSpacesLink: true,
		requiredProduct: "room_booking",
		disabledMessage: "Not available in your subscription",
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "settings",
		icon: <SettingsIcon />,
		label: "Settings",
		path: "/dashboard/settings",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "integrations",
		icon: <IntegrationsIcon />,
		label: "Integrations",
		path: "/dashboard/settings/integrations-plugins",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "automation",
		icon: <AutomationIcon />,
		label: "Automation",
		path: "/dashboard/automation",
		disabled: false,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "referrals",
		icon: <ReferralsIcon />,
		label: "Referrals",
		path: "/dashboard/referrals",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "messaging",
		icon: <MessagingIcon />,
		label: "Messaging",
		path: "/dashboard/messaging",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "kemi",
		icon: <KemiIcon />,
		label: "KeMi (Co-pilot)",
		path: "/dashboard/kemi",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			"STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
	{
		id: "inventory",
		icon: <InventoryIcon />,
		label: "Inventory",
		path: "/dashboard/inventory",
		disabled: true,
		roles: [
			"SUPER_ADMIN",
			"ADMIN",
			"BUSINESS_ADMIN",
			"BUSINESS_MANAGER",
			"LOCATION_ADMIN",
			"LOCATION_MANAGER",
			"STATION_ADMIN",
			// "STATION_MANAGER",
			"TEAM_MEMBER",
		],
	},
];
