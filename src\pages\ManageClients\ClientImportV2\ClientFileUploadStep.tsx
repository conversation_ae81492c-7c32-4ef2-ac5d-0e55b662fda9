import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, FileText, AlertCircle, Download } from "lucide-react";
import { UploadCard } from "@/components/ui-components/Upload";
import { getFileHeaders, getFileSheets, parseFile } from "@/utils/fileParser";
import type { CSVData, Sheet } from "./types";

// Utility function to generate random IDs
const generateRandomId = (): string => {
	return `id_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`;
};

interface ClientFileUploadStepProps {
	onUploadComplete: (data: CSVData) => void;
}

// Mock download function - replace with actual implementation
const downloadSampleCsv = () => {
	console.log("Download sample CSV");
	// TODO: Implement actual download functionality
};

export default function ClientFileUploadStep({
	onUploadComplete,
}: ClientFileUploadStepProps) {
	const [uploadedFile, setUploadedFile] = useState<File | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// No need to load sheets on file upload - we'll do it when continuing

	const handleContinue = async () => {
		if (!uploadedFile) {
			setError("Please select a file to continue.");
			return;
		}

		setIsLoading(true);
		setError(null);

		try {
			// Load all sheets and their headers
			const availableSheets = await getFileSheets(uploadedFile);
			const headers: Record<string, string[]> = {};
			const data: Record<string, Array<Record<string, string>>> = {};

			// Handle CSV files (single sheet) vs Excel files (multiple sheets)
			if (availableSheets.length === 0) {
				// CSV file - treat as single sheet
				const sheetName = "Sheet1";
				const sheetHeaders = await getFileHeaders(uploadedFile);
				headers[sheetName] = sheetHeaders;

				// Parse data rows
				const parseResult = await parseFile(uploadedFile);
				if (parseResult.error) {
					throw new Error(
						`Failed to parse CSV file: ${parseResult.error}`
					);
				}

				// Convert data rows to objects with header keys
				const dataRows: Array<Record<string, string>> = [];
				for (const row of parseResult.data) {
					const rowObject: Record<string, string> = {};
					sheetHeaders.forEach((header, index) => {
						rowObject[header] = row[index] || "";
					});
					dataRows.push(rowObject);
				}
				data[sheetName] = dataRows;

				const sheets: Sheet[] = [{
					id: generateRandomId(),
					label: sheetName,
				}];

				const csvData: CSVData = {
					sheets,
					selectedSheet: sheetName,
					headers,
					data,
				};

				onUploadComplete(csvData);
			} else {
				// Excel file - multiple sheets
				for (const sheet of availableSheets) {
					// Parse headers
					const sheetHeaders = await getFileHeaders(uploadedFile, sheet);
					headers[sheet] = sheetHeaders;

					// Parse data rows
					const parseResult = await parseFile(uploadedFile, sheet);
					if (parseResult.error) {
						throw new Error(
							`Failed to parse sheet ${sheet}: ${parseResult.error}`
						);
					}

					// Convert data rows to objects with header keys
					const dataRows: Array<Record<string, string>> = [];
					for (const row of parseResult.data) {
						const rowObject: Record<string, string> = {};
						sheetHeaders.forEach((header, index) => {
							rowObject[header] = row[index] || "";
						});
						dataRows.push(rowObject);
					}
					data[sheet] = dataRows;
				}

				const sheets: Sheet[] = availableSheets.map((sheetName) => ({
					id: generateRandomId(),
					label: sheetName,
				}));

				const csvData: CSVData = {
					sheets,
					selectedSheet: availableSheets[0] || "",
					headers,
					data,
				};

				onUploadComplete(csvData);
			}
		} catch (error) {
			setError("Failed to process file. Please check your file format.");
			console.error("Error processing file:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleFileUpload = useCallback(() => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".csv,.xlsx,.xls";
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				setUploadedFile(target.files[0]);
				setError(null);
			}
		};
		input.click();
	}, []);

	const handleRemoveFile = useCallback(() => {
		setUploadedFile(null);
	}, []);

	const handleChangeFile = useCallback(() => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".csv,.xlsx,.xls";
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				setUploadedFile(target.files[0]);
				setError(null);
			}
		};
		input.click();
	}, []);

	const handleDownloadSample = useCallback(() => {
		downloadSampleCsv();
	}, []);

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		const file = e.dataTransfer.files[0];
		if (file) {
			setUploadedFile(file);
			setError(null);
		}
	};

	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{/* Info Card */}
			<div className="flex w-full items-start justify-between gap-6 overflow-hidden rounded-lg bg-white p-4 outline-1 outline-offset-[-1px] outline-gray-300">
				<div className="flex flex-1 items-start justify-start gap-2.5">
					<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 p-2.5">
						<Upload className="h-3.5 w-3.5 text-gray-700" />
					</div>
					<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
						<div className="justify-start text-sm leading-tight font-semibold text-gray-900">
							Upload Patient Information
						</div>
						<div className="justify-start text-[10px] leading-3 font-normal text-gray-500">
							Download the excel template, populate the document
							and then upload that file below.
						</div>
					</div>
				</div>
				<Button
					onClick={handleDownloadSample}
					className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
				>
					<Download className="h-3 w-3 text-white" />
					<span className="text-xs leading-none font-medium text-white">
						Download Sample CSV 
					</span>
				</Button>
			</div>

			{error && (
				<div className="mb-4 flex items-center rounded-lg border border-red-200 bg-red-50 p-4">
					<AlertCircle className="mr-2 h-5 w-5 text-red-500" />
					<span className="text-red-700">{error}</span>
				</div>
			)}

			{/* Upload Area or File Display */}
			{!uploadedFile ? (
				<UploadCard
					variant="centered"
					width="w-full"
					title="Click or drag file here to upload file"
					description="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
					buttonText="Browse Files"
					accept=".csv,.xlsx,.xls"
					onBrowseClick={handleFileUpload}
					onDragOver={handleDragOver}
					onDrop={handleDrop}
					className="flex min-h-[400px] w-full flex-col items-center justify-center gap-6 rounded-lg p-6 outline-1 outline-offset-[-1px] outline-gray-300 [&>*:last-child]:w-fit [&>*:last-child]:self-center"
				/>
			) : (
				<div className="flex w-full flex-col items-start justify-start gap-2.5">
					<div className="flex h-[400px] w-full flex-col items-center justify-center gap-6 rounded-lg bg-white p-6 outline-1 outline-offset-[-1px] outline-gray-300">
						<div className="relative h-10 w-10 overflow-hidden">
							<FileText className="h-9 w-7 text-gray-700" />
						</div>
						<div className="flex flex-col items-center justify-start gap-1 self-stretch">
							<div className="justify-start self-stretch text-center text-sm leading-tight font-semibold text-gray-900">
								{uploadedFile.name}
							</div>
							<div className="justify-start self-stretch text-center text-[10px] leading-3 font-normal text-gray-500">
								File size:{" "}
								{(uploadedFile.size / 1024 / 1024).toFixed(2)}mb
							</div>
						</div>
						<div className="inline-flex items-center justify-start gap-2">
							<Button
								onClick={handleRemoveFile}
								className="flex h-9 w-28 items-center justify-center gap-2 rounded-md bg-red-600 px-4 py-2 hover:bg-red-700"
							>
								<span className="text-xs leading-none font-medium text-white">
									Remove
								</span>
							</Button>
							<Button
								onClick={handleChangeFile}
								variant="outline"
								className="flex h-9 w-28 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-gray-300"
							>
								<span className="text-xs leading-none font-medium text-gray-900">
									Change
								</span>
							</Button>
						</div>
					</div>
				</div>
			)}

			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={handleContinue}
						disabled={!uploadedFile || isLoading}
						className={`inline-flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2 ${
							uploadedFile && !isLoading
								? "bg-[#005893] text-white hover:bg-[#004a7a]"
								: "cursor-not-allowed bg-[#005893] text-white opacity-50"
						}`}
					>
						{isLoading ? (
							<div className="flex items-center">
								<div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
								Processing...
							</div>
						) : (
							<span className="text-xs leading-none font-medium">
								Upload
							</span>
						)}
					</Button>
				</div>
			</div>
		</div>
	);
}
