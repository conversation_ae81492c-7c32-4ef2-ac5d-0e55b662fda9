import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Check } from "lucide-react";
import ClientFileUploadStep from "./ClientFileUploadStep";
import SheetImportWrapper from "./SheetImportWrapper";
import ErrorBoundary from "./ErrorBoundary";
import type { CSVData, Sheet, SystemField } from "./types";

export type ImportStep = "UPLOAD" | "IMPORT";

export default function ClientImportWrapper() {
	const [currentStep, setCurrentStep] = useState<ImportStep>("UPLOAD");
	const [csvData, setCsvData] = useState<CSVData | null>(null);
	const [sheets, setSheets] = useState<Sheet[]>([]);
	const [currentSheet, setCurrentSheet] = useState<Sheet | null>(null);
	const [attributes, setAttributes] = useState<SystemField[]>([]);

	const handleFileUploadComplete = (data: CSVData) => {
		setCsvData(data);
		setSheets(data.sheets);
		setCurrentSheet(data.sheets[0] || null);
		setCurrentStep("IMPORT");
	};

	const handleBackToUpload = () => {
		setCurrentStep("UPLOAD");
		setCsvData(null);
		setSheets([]);
		setCurrentSheet(null);
		setAttributes([]);
	};

	const handleSheetChange = (sheet: Sheet) => {
		setCurrentSheet(sheet);
	};

	const renderCurrentStep = () => {
		switch (currentStep) {
			case "UPLOAD":
				return (
					<ClientFileUploadStep
						onUploadComplete={handleFileUploadComplete}
					/>
				);
			case "IMPORT":
				return (
					<SheetImportWrapper
						csvData={csvData!}
						sheets={sheets}
						currentSheet={currentSheet!}
						attributes={attributes}
						setAttributes={setAttributes}
						onBack={handleBackToUpload}
						onSheetChange={handleSheetChange}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="mx-auto flex w-full flex-col items-center justify-start gap-4 py-8">
			{/* Progress Indicator - Matching existing structure */}
			<div className="inline-flex w-96 items-start justify-start gap-2 p-2">
				<div className="flex items-center justify-start gap-1">
					<div className="flex h-4 w-4 items-center justify-between">
						<div className="flex h-4 w-4 items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-[#005893] p-1 outline-1 outline-offset-[-1px] outline-[#005893]">
							<div className="relative h-2 w-2 overflow-hidden">
								<Check className="h-2 w-2 text-white" />
							</div>
						</div>
					</div>
					<div className="text-[10px] leading-3 font-normal text-gray-500">
						Upload File
					</div>
				</div>
				<div className="inline-flex h-4 flex-1 flex-col items-start justify-center gap-2.5 py-1.5">
					<div
						className={`h-px self-stretch ${currentStep === "IMPORT" ? "bg-[#005893]" : "bg-gray-300"}`}
					/>
				</div>
				<div className="flex items-center justify-start gap-1">
					<div className="flex h-4 w-4 items-center justify-between">
						{currentStep === "IMPORT" ? (
							<div className="flex h-4 w-4 items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-[#005893] p-1 outline-1 outline-offset-[-1px] outline-[#005893]">
								<div className="relative h-2 w-2 overflow-hidden">
									<Check className="h-2 w-2 text-white" />
								</div>
							</div>
						) : (
							<div className="inline-flex h-4 w-4 flex-col items-center justify-center gap-2.5 overflow-hidden rounded-lg bg-white p-0.5 outline-1 outline-offset-[-1px] outline-gray-300">
								<div className="h-3 w-3 justify-center text-center text-[8px] leading-3 font-medium text-gray-700">
									02
								</div>
							</div>
						)}
					</div>
					<div className="text-[10px] leading-3 font-normal text-gray-500">
						Import Sheets
					</div>
				</div>
			</div>

			<ErrorBoundary>{renderCurrentStep()}</ErrorBoundary>
		</div>
	);
}
