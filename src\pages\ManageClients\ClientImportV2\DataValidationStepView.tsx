import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	ArrowLeft,
	AlertCircle,
	CheckCircle,
	RefreshCw,
	Info,
} from "lucide-react";
import { InputText } from "@/components/common/InputText/InputText";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useQuery } from "@tanstack/react-query";
import { clientImportV2Api } from "@/lib/api/clientImportV2";
import type {
	CSVData,
	Sheet,
	SheetHeader,
	SystemField,
	DataValidation,
	DataValidationError,
} from "./types";

interface DataValidationStepViewProps {
	csvData: CSVData;
	currentSheet: Sheet;
	sheetHeaders: SheetHeader[];
	attributes: SystemField[];
	onBack: () => void;
	onNext: () => void;
}

export default function DataValidationStepView({
	csvData,
	currentSheet,
	sheetHeaders,
	attributes,
	onBack,
	onNext,
}: DataValidationStepViewProps) {
	const { organizationId } = useOrganizationContext();
	const [validationResult, setValidationResult] =
		useState<DataValidation | null>(null);
	const [isValidating, setIsValidating] = useState(false);
	const [showSuccessMessage, setShowSuccessMessage] = useState(false);
	const [isCommitting, setIsCommitting] = useState(false);
	const [commitResult, setCommitResult] = useState<{
		created_count: number;
		updated_count: number;
		total_processed: number;
	} | null>(null);

	const [processedData, setProcessedData] = useState<
		Array<Record<string, string>>
	>([]);

	// Get CSV data for current sheet
	const sheetData = useMemo(() => {
		// Check if csvData and csvData.data exist
		if (!csvData || !csvData.data) {
			console.warn("CSV data is not available");
			console.log("csvData:", csvData);
			return [];
		}

		// Check if the current sheet data exists
		const data = csvData.data[currentSheet.label];
		if (!data) {
			console.warn(`No data found for sheet: ${currentSheet.label}`);
			console.log("Available sheets:", Object.keys(csvData.data));
			console.log("Current sheet label:", currentSheet.label);
			return [];
		}

		console.log(
			`Found ${data.length} rows for sheet: ${currentSheet.label}`
		);
		return data;
	}, [csvData, currentSheet.label]);

	// Transform data to use systemFieldKey as keys instead of tempIds
	const transformedData = useMemo(() => {
		console.log("Transforming data:", { sheetData, sheetHeaders });
		return sheetData.map((row) => {
			const transformedRow: Record<string, string> = {};
			sheetHeaders.forEach((header) => {
				// Only include mapped headers (those with systemFieldKey)
				if (header.systemFieldKey) {
					// Use systemFieldKey as key, and get value from the original row using labelOnFile
					transformedRow[header.systemFieldKey] =
						row[header.labelOnFile || ""] || "";
				}
			});
			return transformedRow;
		});
	}, [sheetData, sheetHeaders]);

	// Initialize processed data when sheet data changes
	useEffect(() => {
		setProcessedData(transformedData);
	}, [transformedData]);

	// Get system field by key
	const getSystemField = (key: string): SystemField | undefined => {
		return attributes.find((attr) => attr.key === key);
	};

	// Get errors for a specific row
	const getRowErrors = (rowIndex: number): DataValidationError[] => {
		if (!validationResult?.data_errors) return [];
		return validationResult.data_errors.filter(
			(error) => error.row_index === rowIndex
		);
	};

	// Get errors for a specific cell
	const getCellErrors = (
		rowIndex: number,
		systemFieldKey: string
	): DataValidationError[] => {
		return getRowErrors(rowIndex).filter(
			(error) => error.system_field_key === systemFieldKey
		);
	};

	// Check if a row has errors
	const hasRowErrors = (rowIndex: number): boolean => {
		return getRowErrors(rowIndex).length > 0;
	};

	// Handle cell value change
	const handleCellValueChange = (
		rowIndex: number,
		systemFieldKey: string,
		value: string
	) => {
		setProcessedData((prev) => {
			const newData = [...prev];
			if (!newData[rowIndex]) {
				newData[rowIndex] = {};
			}
			newData[rowIndex][systemFieldKey] = value;
			return newData;
		});
	};

	// Validate data
	const handleValidateData = async () => {
		if (!organizationId) return;

		// Check if we have any data to validate
		if (transformedData.length === 0) {
			setValidationResult({
				is_valid: false,
				error_type: "Header",
				message:
					"No data available for validation. Please ensure the file contains data.",
				header_errors: {},
				data_errors: [],
			});
			return;
		}

		setIsValidating(true);
		setValidationResult(null);
		setShowSuccessMessage(false);

		try {
			const requestData = {
				sheetId: currentSheet.id,
				data: transformedData,
			};

			console.log("Validation request data:", requestData);

			const result = await clientImportV2Api.validateData(
				requestData,
				organizationId
			);

			console.log("Validation result:", result);
		} catch (error: any) {
			console.error("Data validation failed:", error);
			// Handle error response
			if (error?.response?.data) {
				setValidationResult(error.response.data);
			}
		} finally {
			setIsValidating(false);
		}
	};

	// Commit data to database
	const handleCommitData = async () => {
		if (!organizationId) return;

		// Check if we have any data to commit
		if (transformedData.length === 0) {
			return;
		}

		// Check if validation has passed
		if (hasValidationErrors()) {
			return;
		}

		setIsCommitting(true);
		setCommitResult(null);

		try {
			const requestData = {
				sheetId: currentSheet.id,
				data: transformedData,
			};

			console.log("Commit request data:", requestData);

			const result = await clientImportV2Api.commitData(
				requestData,
				organizationId
			);

			setCommitResult(result.data);
			console.log("Commit result:", result);
		} catch (error: any) {
			console.error("Data commit failed:", error);
			// Handle error response
			if (error?.response?.data) {
				setValidationResult(error.response.data);
			}
		} finally {
			setIsCommitting(false);
		}
	};

	// Check if validation has errors
	const hasValidationErrors = (): boolean => {
		if (!validationResult) return false;
		return !validationResult.is_valid;
	};

	// Get total error count
	const getTotalErrorCount = (): number => {
		if (!validationResult?.data_errors) return 0;
		return validationResult.data_errors.length;
	};

	// Check if we have sheet headers
	if (sheetHeaders.length === 0) {
		return (
			<div className="flex w-full flex-col items-start justify-start gap-4">
				<div className="w-full rounded-lg border border-yellow-200 bg-yellow-50 p-4">
					<div className="text-center">
						<p className="text-yellow-700">
							No headers available. Please go back to the mapping
							step to configure headers.
						</p>
					</div>
				</div>
				<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
					<Button
						onClick={onBack}
						variant="ghost"
						className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2"
					>
						<ArrowLeft className="h-4 w-4" />
						<span className="text-xs leading-none font-medium text-gray-900">
							Back to Mapping
						</span>
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="flex w-full flex-col items-start justify-start gap-4">
			{/* Debug Info - Remove in production */}
			{process.env.NODE_ENV != "production" && (
				<div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
					<div className="text-sm text-blue-700">
						<strong>Debug Info:</strong>
						<br />
						Headers: {sheetHeaders.length} | Mapped:{" "}
						{sheetHeaders.filter((h) => h.systemFieldKey).length} |
						Mapped Headers:{" "}
						{
							sheetHeaders.filter(
								(header) => header.systemFieldKey
							).length
						}
					</div>
				</div>
			)}

			{/* Success Message */}
			{showSuccessMessage && (
				<div className="rounded-lg border border-green-200 bg-green-50 p-4">
					<div className="flex items-center">
						<CheckCircle className="mr-2 h-5 w-5 text-green-500" />
						<span className="font-medium text-green-700">
							🎉 Data validation successful! All rows are valid
							and ready for import.
						</span>
					</div>
				</div>
			)}

			{/* Commit Success Message */}
			{commitResult && (
				<div className="rounded-lg border border-green-200 bg-green-50 p-4">
					<div className="flex items-center">
						<CheckCircle className="mr-2 h-5 w-5 text-green-500" />
						<span className="font-medium text-green-700">
							🎉 Data committed successfully!
						</span>
					</div>
					<div className="mt-2 text-sm text-green-600">
						Created: {commitResult.created_count} | Updated:{" "}
						{commitResult.updated_count} | Total Processed:{" "}
						{commitResult.total_processed}
					</div>
				</div>
			)}

			{/* Error Summary */}
			{validationResult && !validationResult.is_valid && (
				<div className="rounded-lg border border-red-200 bg-red-50 p-4">
					<div className="flex items-center justify-between">
						<div className="flex items-center">
							<AlertCircle className="mr-2 h-5 w-5 text-red-500" />
							<span className="text-red-700">
								❌ Data validation failed. Found{" "}
								{getTotalErrorCount()} error(s) in{" "}
								{validationResult.data_errors?.length || 0}{" "}
								row(s).
							</span>
						</div>
						<div className="text-sm text-red-600">
							Error Type: {validationResult.error_type}
						</div>
					</div>
					{validationResult.message && (
						<div className="mt-2 text-sm text-red-600">
							{validationResult.message}
						</div>
					)}
					{validationResult.error_type === "Header" && (
						<div className="mt-3">
							<Button
								onClick={onBack}
								variant="outline"
								size="sm"
								className="flex items-center gap-2"
							>
								<ArrowLeft className="h-4 w-4" />
								Go Back to Mapping
							</Button>
						</div>
					)}
				</div>
			)}

			{/* Data Table */}
			{processedData.length === 0 ? (
				<div className="w-full rounded-lg border border-gray-200 bg-gray-50 p-8">
					<div className="text-center">
						<p className="mb-2 text-gray-600">
							No data available for this sheet
						</p>
						<p className="text-sm text-gray-500">
							Please ensure the CSV file contains data for the
							selected sheet.
						</p>
					</div>
				</div>
			) : (
				<div className="w-full overflow-x-auto">
					<div className="min-w-full rounded-lg border border-gray-200">
						{/* Table Header */}
						<div className="bg-gray-50 px-4 py-3">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold text-gray-900">
									Data Preview - {currentSheet.label}
								</h3>
								<div className="flex items-center gap-2">
									<span className="text-sm text-gray-600">
										{processedData.length} rows
									</span>
									{validationResult && (
										<span
											className={`text-sm ${validationResult.is_valid ? "text-green-600" : "text-red-600"}`}
										>
											{getTotalErrorCount()} errors
										</span>
									)}
								</div>
							</div>
						</div>

						{/* Table Content */}
						<div className="max-h-96 overflow-y-auto">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="sticky top-0 bg-gray-50">
									<tr>
										<th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
											Row
										</th>
										{sheetHeaders
											.filter(
												(header) =>
													header.systemFieldKey
											)
											.map((header) => (
												<th
													key={header.systemFieldKey}
													className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
												>
													<div className="flex flex-col">
														<span>
															{header.labelOnFile}
														</span>
														{header.systemField && (
															<span className="text-xs text-gray-400">
																{
																	header
																		.systemField
																		.label
																}
															</span>
														)}
													</div>
												</th>
											))}
									</tr>
								</thead>
								<tbody className="divide-y divide-gray-200 bg-white">
									{processedData.map((row, rowIndex) => {
										const rowErrors =
											getRowErrors(rowIndex);
										const hasErrors =
											hasRowErrors(rowIndex);

										return (
											<tr
												key={rowIndex}
												className={`${
													hasErrors
														? "border-l-4 border-red-400 bg-red-50"
														: "hover:bg-gray-50"
												}`}
											>
												<td className="px-4 py-3 text-sm text-gray-900">
													{rowIndex + 1}
												</td>
												{sheetHeaders
													.filter(
														(header) =>
															header.systemFieldKey
													)
													.map((header) => {
														const cellErrors =
															getCellErrors(
																rowIndex,
																header.systemFieldKey!
															);
														const value =
															row[
																header
																	.systemFieldKey!
															] || "";

														return (
															<td
																key={
																	header.systemFieldKey
																}
																className="px-4 py-3"
															>
																<InputText
																	value={
																		value
																	}
																	onChange={(
																		e
																	) =>
																		handleCellValueChange(
																			rowIndex,
																			header.systemFieldKey!,
																			e
																				.target
																				.value
																		)
																	}
																	className={`w-full ${
																		cellErrors.length >
																		0
																			? "border-red-300 focus:border-red-500"
																			: ""
																	}`}
																/>
																{cellErrors.length >
																	0 && (
																	<div className="mt-1 text-xs break-words text-red-600">
																		{cellErrors.map(
																			(
																				error,
																				index
																			) => (
																				<div
																					key={
																						index
																					}
																					className="whitespace-normal"
																				>
																					{
																						error.message
																					}
																				</div>
																			)
																		)}
																	</div>
																)}
															</td>
														);
													})}
											</tr>
										);
									})}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			)}

			{/* Action Buttons */}
			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<Button
					onClick={onBack}
					variant="ghost"
					className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2"
				>
					<ArrowLeft className="h-4 w-4" />
					<span className="text-xs leading-none font-medium text-gray-900">
						Back
					</span>
				</Button>

				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={handleValidateData}
						variant="outline"
						disabled={isValidating || processedData.length === 0}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-[#005893]"
					>
						{isValidating ? (
							<>
								<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
								Validating...
							</>
						) : (
							<>
								<Info className="mr-2 h-4 w-4" />
								<span className="text-xs leading-none font-medium text-[#005893]">
									Validate Data
								</span>
							</>
						)}
					</Button>

					{!commitResult ? (
						<Button
							onClick={handleCommitData}
							disabled={
								hasValidationErrors() ||
								isValidating ||
								isCommitting
							}
							className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
						>
							{isCommitting ? (
								<>
									<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
									<span className="text-xs leading-none font-medium text-white">
										Committing...
									</span>
								</>
							) : (
								<span className="text-xs leading-none font-medium text-white">
									Commit Data
								</span>
							)}
						</Button>
					) : (
						<Button
							onClick={onNext}
							className="flex h-9 items-center justify-center gap-2 rounded-md bg-green-600 px-4 py-2 hover:bg-green-700"
						>
							<span className="text-xs leading-none font-medium text-white">
								Continue
							</span>
						</Button>
					)}
				</div>
			</div>
		</div>
	);
}
