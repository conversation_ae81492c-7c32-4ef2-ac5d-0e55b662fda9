import React from "react";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";

interface ErrorBoundaryState {
	hasError: boolean;
	error?: Error;
}

interface ErrorBoundaryProps {
	children: React.ReactNode;
	fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): ErrorBoundaryState {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("ErrorBoundary caught an error:", error, errorInfo);
	}

	resetError = () => {
		this.setState({ hasError: false, error: undefined });
	};

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				const FallbackComponent = this.props.fallback;
				return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;
			}

			return (
				<div className="flex min-h-[400px] flex-col items-center justify-center space-y-4 p-6">
					<div className="flex items-center space-x-2 text-red-600">
						<AlertCircle className="h-8 w-8" />
						<h2 className="text-xl font-semibold">Something went wrong</h2>
					</div>
					<p className="text-center text-gray-600 max-w-md">
						An unexpected error occurred while processing your import. Please try again or contact support if the problem persists.
					</p>
					{process.env.NODE_ENV === "development" && this.state.error && (
						<details className="w-full max-w-md">
							<summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
								Error Details (Development)
							</summary>
							<pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
								{this.state.error.message}
								{this.state.error.stack}
							</pre>
						</details>
					)}
					<div className="flex space-x-2">
						<Button onClick={this.resetError} variant="outline">
							<RefreshCw className="mr-2 h-4 w-4" />
							Try Again
						</Button>
						<Button onClick={() => window.location.reload()}>
							Reload Page
						</Button>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default ErrorBoundary; 