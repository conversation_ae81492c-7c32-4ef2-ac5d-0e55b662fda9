import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	ArrowLeft,
	Plus,
	AlertCircle,
	CheckCircle,
	RefreshCw,
	Info,
} from "lucide-react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { InputText } from "@/components/common/InputText/InputText";
import { Switch } from "@/components/ui/switch";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { clientImportV2Api } from "@/lib/api/clientImportV2";
import { autoMatchColumns } from "@/utils/fileParser";
import type {
	CSVData,
	Sheet,
	She<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>Field,
	ValidationResponse,
	AddAttributeRequest,
} from "./types";

// Utility function to generate random IDs
const generateRandomId = (): string => {
	return `id_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`;
};

interface SheetAttributeMapStepViewProps {
	csvData: CSVData;
	currentSheet: Sheet;
	attributes: SystemField[];
	setAttributes: (attributes: SystemField[]) => void;
	onBack: () => void;
	onNext: (headers?: SheetHeader[]) => void;
}

export default function SheetAttributeMapStepView({
	csvData,
	currentSheet,
	attributes,
	setAttributes,
	onBack,
	onNext,
}: SheetAttributeMapStepViewProps) {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();
	const [sheetHeaders, setSheetHeaders] = useState<SheetHeader[]>([]);
	const [validationResult, setValidationResult] =
		useState<ValidationResponse | null>(null);
	const [isValidating, setIsValidating] = useState(false);
	const [showSuccessMessage, setShowSuccessMessage] = useState(false);
	const [showAddAttributeDialog, setShowAddAttributeDialog] = useState(false);
	const [creatingForHeaderId, setCreatingForHeaderId] = useState<
		string | null
	>(null);
	const [newAttributeData, setNewAttributeData] =
		useState<AddAttributeRequest>({
			labelOnFile: "",
			label: "",
			type: "text",
			options: [],
		});

	// Fetch system attributes if not already loaded
	const { data: attributesData, isLoading: isLoadingAttributes } = useQuery({
		queryKey: ["clientImportV2Attributes", organizationId],
		queryFn: () => clientImportV2Api.getAttributes(organizationId || 0),
		enabled: !!organizationId && attributes.length === 0,
	});

	const systemFields: SystemField[] = useMemo(() => {
		if (attributes.length > 0) return attributes;

		return (attributesData?.data || []).map((attr: any) => ({
			key: attr.key,
			label: attr.label,
			is_system_field: attr.is_system_field,
			is_validator: attr.is_validator,
			is_required: attr.is_required,
			type: attr.type,
			options: attr.options || [],
		}));
	}, [attributes, attributesData]);

	// Re-initialize headers when system fields are loaded
	useEffect(() => {
		if (
			currentSheet &&
			systemFields.length > 0 &&
			sheetHeaders.length > 0
		) {
			// Check if any headers are unmapped
			const unmappedHeaders = sheetHeaders.filter(
				(h) => !h.systemFieldKey
			);
			if (unmappedHeaders.length > 0) {
				console.log(
					"Re-initializing headers with system fields for unmapped headers"
				);
				initializeHeaders();
			}
		}
	}, [systemFields.length, currentSheet, sheetHeaders.length]);

	// Update attributes when fetched
	useEffect(() => {
		if (attributesData?.data && attributes.length === 0) {
			const fetchedAttributes = (attributesData.data || []).map(
				(attr: any) => ({
					key: attr.key,
					label: attr.label,
					is_system_field: attr.is_system_field,
					is_validator: attr.is_validator,
					is_required: attr.is_required,
					type: attr.type,
					options: attr.options || [],
				})
			);
			setAttributes(fetchedAttributes);
		}
	}, [attributesData, attributes.length, setAttributes]);

	// Initialize headers when current sheet or system fields change
	useEffect(() => {
		console.log("useEffect triggered:", {
			currentSheet: currentSheet?.label,
			systemFieldsLength: systemFields.length,
			hasCurrentSheet: !!currentSheet,
			hasSystemFields: systemFields.length > 0,
		});

		if (currentSheet) {
			if (systemFields.length > 0) {
				initializeHeaders();
			} else {
				// Initialize headers without auto-mapping if system fields aren't loaded yet
				const fileHeaders = csvData.headers[currentSheet.label] || [];
				const headers: SheetHeader[] = fileHeaders.map((header) => ({
					tempId: generateRandomId(),
					sheetId: currentSheet.id,
					labelOnFile: header,
					systemFieldKey: null,
					systemField: undefined,
					errors: [],
				}));
				console.log(
					"Initializing headers without system fields:",
					headers
				);
				setSheetHeaders(headers);
			}
			// Clear validation results when switching sheets
			setValidationResult(null);
			setShowSuccessMessage(false);
		}
	}, [currentSheet, systemFields, csvData.headers]);

	const initializeHeaders = () => {
		const fileHeaders = csvData.headers[currentSheet.label] || [];
		console.log("Initializing headers for sheet:", currentSheet.label);
		console.log("File headers:", fileHeaders);
		console.log("Available system fields:", systemFields);

		// Auto-match headers
		const availableFields = systemFields.map((field) => ({
			key: field.key,
			label: field.label,
			type: field.type,
		}));

		const autoMatchResults = autoMatchColumns(fileHeaders, availableFields);
		console.log("Auto-match results:", autoMatchResults);

		const headers: SheetHeader[] = fileHeaders.map((header, index) => {
			const matchResult = autoMatchResults[index];
			const matchedField = systemFields.find(
				(f) => f.key === matchResult.matchedField?.key
			);

			const headerObj = {
				tempId: generateRandomId(),
				sheetId: currentSheet.id,
				labelOnFile: header,
				systemFieldKey: matchedField?.key || null,
				systemField: matchedField,
				errors: [],
			};

			console.log(
				`Header "${header}" mapped to:`,
				matchedField?.key || "null"
			);
			return headerObj;
		});

		console.log("Final headers:", headers);
		setSheetHeaders(headers);
	};

	const handleSystemFieldChange = (
		headerId: string,
		systemFieldKey: string | null
	) => {
		if (systemFieldKey === "CREATE_NEW") {
			setCreatingForHeaderId(headerId);
			setNewAttributeData((prev) => ({
				...prev,
				labelOnFile:
					sheetHeaders.find((h) => h.tempId === headerId)
						?.labelOnFile || "",
			}));
			setShowAddAttributeDialog(true);
			return;
		}

		// Handle "none" value
		const actualSystemFieldKey =
			systemFieldKey === "none" ? null : systemFieldKey;

		setSheetHeaders((prev) =>
			prev.map((header) => {
				if (header.tempId === headerId) {
					const systemField = actualSystemFieldKey
						? systemFields.find(
								(f) => f.key === actualSystemFieldKey
							)
						: undefined;
					return {
						...header,
						systemFieldKey: actualSystemFieldKey,
						systemField,
						errors: [], // Clear errors when field changes
					};
				}
				return header;
			})
		);
	};

	const handleAddAttribute = async () => {
		try {
			const response = await clientImportV2Api.addAttribute(
				newAttributeData,
				organizationId || 0
			);

			// Invalidate and refetch the attributes query
			await queryClient.invalidateQueries({
				queryKey: ["clientImportV2Attributes", organizationId],
			});

			// If we have the new attribute data from the response, add it to the current attributes
			if (response?.data) {
				const newAttribute: SystemField = {
					key: response.data.key || `custom_${Date.now()}`,
					label: newAttributeData.label,
					is_system_field: false,
					is_required: false,
					type: newAttributeData.type,
					options: newAttributeData.options || [],
				};

				setAttributes([...attributes, newAttribute]);

				// Automatically select the new field in the header that triggered the creation
				if (creatingForHeaderId) {
					setSheetHeaders((prev) =>
						prev.map((header) => {
							if (header.tempId === creatingForHeaderId) {
								return {
									...header,
									systemFieldKey: newAttribute.key,
									systemField: newAttribute,
									errors: [], // Clear errors when field changes
								};
							}
							return header;
						})
					);
				}
			}

			setShowAddAttributeDialog(false);
			setCreatingForHeaderId(null);
			setNewAttributeData({
				labelOnFile: "",
				label: "",
				type: "text",
				options: [],
			});
		} catch (error) {
			console.error("Failed to add attribute:", error);
		}
	};

	const handleValidatorToggle = (headerId: string, checked: boolean) => {
		setSheetHeaders((prev) =>
			prev.map((header) =>
				header.tempId === headerId
					? { ...header, isValidator: checked }
					: header
			)
		);
	};

	const handleTestRowValidation = async (header: SheetHeader) => {
		try {
			if (!header.systemFieldKey || header.systemFieldKey === "none") {
				alert("Please select a system field first.");
				return;
			}

			const requestData = {
				sheetId: currentSheet.id,
				headers: [
					{
						tempId: header.tempId,
						labelOnFile: header.labelOnFile,
						systemFieldKey: header.systemFieldKey,
					},
				],
			};

			const result = await clientImportV2Api.validateAttributeMapping(
				requestData,
				organizationId || 0
			);

			if (result.is_valid) {
				alert("✅ Row validation successful!");
			} else {
				alert(
					`❌ Row validation failed: ${result.header_errors?.[header.tempId]?.[0]?.message || "Unknown error"}`
				);
			}
		} catch (error: any) {
			console.error("Row validation error:", error);
			const errorMessage =
				error.response?.data?.message ||
				error.message ||
				"Validation failed";
			alert(`❌ Row validation error: ${errorMessage}`);
		}
	};

	const handleNewFieldLabelChange = (headerId: string, label: string) => {
		setSheetHeaders((prev) =>
			prev.map((header) =>
				header.tempId === headerId
					? { ...header, newFieldLabel: label }
					: header
			)
		);
	};

	const handleNewFieldTypeChange = (
		headerId: string,
		type: "text" | "email" | "phone" | "number" | "date" | "select"
	) => {
		setSheetHeaders((prev) =>
			prev.map((header) =>
				header.tempId === headerId
					? { ...header, newFieldType: type }
					: header
			)
		);
	};

	const handleTestValidation = async () => {
		if (!organizationId) return;

		setIsValidating(true);
		setValidationResult(null); // Clear previous validation results
		setShowSuccessMessage(false); // Clear success message

		try {
			const requestData = {
				sheetId: currentSheet.id,
				headers: prepareHeadersData(),
			};

			const result = await clientImportV2Api.validateAttributeMapping(
				requestData,
				organizationId
			);

			parseResult(result);
		} catch (error) {
			console.error("Validation Failed Response:", error);

			// Try to parse validation errors from the API response
			parseApiError(error);
		} finally {
			setIsValidating(false);
		}
	};

	const prepareHeadersData = () => {
		return sheetHeaders.map((header) => ({
			tempId: header.tempId,
			labelOnFile: header.labelOnFile,
			systemFieldKey: header.systemFieldKey,
			is_validator: header.isValidator || false,
		}));
	};

	const handleNextStep = () => {
		// Clear validation results when proceeding to next step
		setValidationResult(null);
		setShowSuccessMessage(false);

		// Prepare headers data with is_validator field
		const headersData = prepareHeadersData();

		// Pass the sheet headers to the parent component
		onNext(sheetHeaders);
	};

	const getAvailableSystemFields = (currentHeaderId: string) => {
		const usedKeys = new Set<string>();

		sheetHeaders.forEach((header) => {
			if (header.tempId !== currentHeaderId && header.systemFieldKey) {
				usedKeys.add(header.systemFieldKey);
			}
		});

		return systemFields.filter((field) => !usedKeys.has(field.key));
	};

	const hasValidationErrors = () => {
		if (!validationResult) return false;

		return (
			!validationResult.is_valid ||
			sheetHeaders.some((header) => header.errors.length > 0)
		);
	};

	const parseResult = (responseData: any) => {
		try {
			console.log("Parsing result:", responseData);
			// Check if it's a validation error response
			if (responseData.is_valid === false) {
				console.log("Found validation error data:", {
					sheet_errors: responseData.sheet_errors,
					header_errors: responseData.header_errors,
				});

				const result = {
					is_valid: false,
					sheet_errors: responseData.sheet_errors || {},
					header_errors: responseData.header_errors || {},
				};
				setValidationResult(result);
				setShowSuccessMessage(false);
			} else {
				const result = {
					is_valid: true,
					sheet_errors: {},
					header_errors: {},
				};
				setValidationResult(result);
				setShowSuccessMessage(true);
			}
		} catch (parseError) {
			console.error("Error parsing API error:", parseError);

			setValidationResult({
				is_valid: false,
				sheet_errors: {
					general: [
						{
							type: "general_validation",
							message:
								"Validation failed. Please check your mapping and try again.",
						},
					],
				},
			});
			setShowSuccessMessage(false);
		}
	};

	// Parse API error response to extract validation data
	const parseApiError = (error: any): ValidationResponse | null => {
		try {
			console.log("Parsing API error:", error);

			// Check if error has response data with validation information
			if (error?.response?.data) {
				const responseData = error.response.data;
				console.log("Response data:", responseData);

				parseResult(responseData);
			}

			// TODO: Handle GENERAL ERRORS
			// If no validation data found, return null
			console.log("No validation data found in error response");
			return null;
		} catch (parseError) {
			console.error("Error parsing API error:", parseError);
			// TODO: Handle GENERAL ERRORS
			return null;
		}
	};

	// Frontend validation for immediate feedback
	const validateHeadersLocally = () => {
		const errors: Record<string, any[]> = {};

		// Check for empty labels
		sheetHeaders.forEach((header) => {
			if (!header.labelOnFile || header.labelOnFile.trim() === "") {
				if (!errors[header.tempId]) errors[header.tempId] = [];
				errors[header.tempId].push({
					type: "empty_label",
					message: "Column label cannot be empty.",
				});
			}
		});

		// Check for duplicate labels
		const labelCounts: Record<string, number> = {};
		sheetHeaders.forEach((header) => {
			if (header.labelOnFile) {
				labelCounts[header.labelOnFile] =
					(labelCounts[header.labelOnFile] || 0) + 1;
			}
		});

		Object.entries(labelCounts).forEach(([label, count]) => {
			if (count > 1) {
				sheetHeaders.forEach((header) => {
					if (header.labelOnFile === label) {
						if (!errors[header.tempId]) errors[header.tempId] = [];
						errors[header.tempId].push({
							type: "duplicate_label",
							message: `Duplicate column label: '${label}' appears ${count} times.`,
						});
					}
				});
			}
		});

		// Check for duplicate system field keys
		const systemKeyCounts: Record<string, number> = {};
		sheetHeaders.forEach((header) => {
			if (header.systemFieldKey) {
				systemKeyCounts[header.systemFieldKey] =
					(systemKeyCounts[header.systemFieldKey] || 0) + 1;
			}
		});

		Object.entries(systemKeyCounts).forEach(([key, count]) => {
			if (count > 1) {
				sheetHeaders.forEach((header) => {
					if (header.systemFieldKey === key) {
						if (!errors[header.tempId]) errors[header.tempId] = [];
						errors[header.tempId].push({
							type: "duplicated_system_field_key",
							message: `System field '${key}' is mapped to multiple columns.`,
						});
					}
				});
			}
		});

		return errors;
	};

	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{/* Success Message */}
			{showSuccessMessage && (
				<div className="rounded-lg border border-green-200 bg-green-50 p-4">
					<div className="flex items-center">
						<CheckCircle className="mr-2 h-5 w-5 text-green-500" />
						<span className="font-medium text-green-700">
							🎉 Validation successful! All mappings are correct.
							You can proceed to the next step.
						</span>
					</div>
				</div>
			)}

			{/* Error Message */}
			{validationResult && !validationResult.is_valid && (
				<div className="rounded-lg border border-red-200 bg-red-50 p-4">
					<div className="flex items-center">
						<AlertCircle className="mr-2 h-5 w-5 text-red-500" />
						<span className="text-red-700">
							❌ Please fix the mapping errors below before
							proceeding.
						</span>
					</div>
				</div>
			)}

			{isLoadingAttributes ? (
				<div className="flex items-center justify-center py-8">
					<div className="text-center">
						<div className="mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
						<p className="text-gray-600">
							Loading system fields...
						</p>
					</div>
				</div>
			) : (
				<div className="flex flex-col items-start justify-start self-stretch rounded-2xl p-2">
					<div className="inline-flex items-center justify-start gap-3 self-stretch pb-2">
						<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
							<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
								Column Titles from Excel
							</div>
						</div>
						<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
							<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
								Titles on Migranium
							</div>
						</div>
						<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
							<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
								Title Type
							</div>
						</div>
						<div className="inline-flex w-28 min-w-20 flex-col items-start justify-center gap-0.5">
							<div className="inline-flex items-center justify-start gap-0.5">
								<div className="justify-center text-xs leading-none font-medium text-gray-500">
									Validator
								</div>
								<Info className="h-3 w-3 text-gray-500" />
							</div>
							<div className="w-24 justify-start text-[8px] leading-3 font-normal text-gray-400">
								(0 or 2+ selected)
							</div>
						</div>
						<div className="flex w-20 min-w-20 items-center justify-start">
							<div className="justify-center text-xs leading-none font-medium text-gray-500">
								Test
							</div>
						</div>
					</div>

					{sheetHeaders.length === 0 ? (
						<div className="flex items-center justify-center py-8 text-gray-500">
							<div className="text-center">
								<p className="text-sm">
									Loading CSV headers...
								</p>
							</div>
						</div>
					) : (
						sheetHeaders.map((header) => {
							const availableFields = getAvailableSystemFields(
								header.tempId
							);
							const localErrors =
								validateHeadersLocally()[header.tempId] || [];
							const backendErrors =
								validationResult?.header_errors?.[
									header.tempId
								] || [];

							// Get sheet-level errors that affect this header
							const sheetErrors =
								validationResult?.sheet_errors?.[
									currentSheet.id
								] || [];
							const affectingSheetErrors = sheetErrors.filter(
								(error) =>
									error.affectedFields &&
									error.affectedFields.includes(header.tempId)
							);

							const allErrors = [
								...localErrors,
								...backendErrors,
								...affectingSheetErrors.map((error) => ({
									type: error.type,
									message: error.message,
								})),
							];
							const hasError = allErrors.length > 0;

							return (
								<div
									key={header.tempId}
									className={`flex flex-col items-start justify-start gap-2 self-stretch border-t border-gray-200 py-2 ${
										hasError
											? "border-red-200 bg-red-50"
											: ""
									}`}
								>
									{/* Main row with all the fields */}
									<div className="inline-flex w-full items-center justify-start gap-3 self-stretch">
										<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
											<div className="flex flex-col items-start justify-start gap-2 self-stretch">
												<input
													value={
														header.labelOnFile || ""
													}
													readOnly
													className="h-9 w-full rounded-md border-gray-300 bg-gray-50/35 px-3 py-2 text-xs font-normal text-gray-900 focus:outline-none"
													placeholder="Column title"
												/>
											</div>
										</div>

										<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
											<Select
												value={
													header.systemFieldKey
														? header.systemFieldKey
														: "none"
												}
												onValueChange={(
													value: string | string[]
												) => {
													const stringValue =
														Array.isArray(value)
															? value[0]
															: value;
													handleSystemFieldChange(
														header.tempId,
														stringValue === "none"
															? null
															: stringValue
													);
												}}
												disabled={isLoadingAttributes}
											>
												<SelectTrigger className="h-9 w-full text-xs">
													<SelectValue placeholder="Select Migranium field" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="none">
														None
													</SelectItem>
													{availableFields.map(
														(field) => (
															<SelectItem
																key={field.key}
																value={
																	field.key
																}
															>
																{field.label}
															</SelectItem>
														)
													)}
													<SelectItem value="CREATE_NEW">
														Create New Field
													</SelectItem>
												</SelectContent>
											</Select>
										</div>

										<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
											{header.systemFieldKey ===
											"CREATE_NEW" ? (
												<Select
													value={
														header.newFieldType ||
														"text"
													}
													onValueChange={(value) =>
														handleNewFieldTypeChange(
															header.tempId,
															value as
																| "text"
																| "email"
																| "phone"
																| "number"
																| "date"
																| "select"
														)
													}
												>
													<SelectTrigger className="h-9 w-full text-xs">
														<SelectValue placeholder="Select Type" />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="text">
															Text
														</SelectItem>
														<SelectItem value="email">
															Email
														</SelectItem>
														<SelectItem value="phone">
															Phone
														</SelectItem>
														<SelectItem value="number">
															Number
														</SelectItem>
														<SelectItem value="date">
															Date
														</SelectItem>
														<SelectItem value="select">
															Select
														</SelectItem>
													</SelectContent>
												</Select>
											) : (
												<div className="inline-flex h-9 items-center justify-start self-stretch overflow-hidden rounded-md bg-gray-50/35 px-3 py-2 outline-1 outline-offset-[-1px] outline-gray-300">
													<div className="flex-1 justify-start text-xs leading-none font-normal text-gray-600 capitalize">
														{header.systemField
															?.type ||
															"Select field first"}
													</div>
												</div>
											)}
										</div>

										<div className="flex w-28 items-center justify-start gap-1 overflow-hidden py-2">
											<div className="flex items-start justify-start gap-2">
												<div className="flex items-center justify-start gap-1.5">
													<Switch
														checked={
															header.isValidator ||
															false
														}
														onCheckedChange={(
															checked
														) =>
															handleValidatorToggle(
																header.tempId,
																checked
															)
														}
													/>
													<div className="h-5 w-5 justify-center text-xs leading-none font-normal text-gray-500">
														{header.isValidator
															? "On"
															: "Off"}
													</div>
												</div>
											</div>
										</div>

										<div className="flex w-20 items-center justify-start py-2">
											<Button
												onClick={() =>
													handleTestRowValidation(
														header
													)
												}
												variant="outline"
												size="sm"
												className="h-7 px-2 text-xs"
												disabled={
													!header.systemFieldKey ||
													header.systemFieldKey ===
														"none"
												}
											>
												Test
											</Button>
										</div>
									</div>

									{/* Error row - displayed below the main row */}
									{hasError && (
										<div className="w-full rounded-md border border-red-200 bg-red-50 px-4 py-2">
											<div className="text-sm text-red-600">
												{allErrors.map(
													(error, index) => (
														<div
															key={index}
															className="flex items-start gap-2"
														>
															<span className="text-red-500">
																•
															</span>
															<span>
																{error.message}
															</span>
														</div>
													)
												)}
											</div>
										</div>
									)}
								</div>
							);
						})
					)}
				</div>
			)}

			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<Button
					onClick={onBack}
					variant="ghost"
					className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2"
				>
					<span className="text-xs leading-none font-medium text-gray-900">
						Back
					</span>
				</Button>

				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={handleTestValidation}
						variant="outline"
						disabled={isValidating}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-[#005893]"
					>
						{isValidating ? (
							<>
								<div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
								Testing...
							</>
						) : (
							<span className="text-xs leading-none font-medium text-[#005893]">
								Test
							</span>
						)}
					</Button>

					<Button
						onClick={handleNextStep}
						disabled={
							hasValidationErrors() ||
							isLoadingAttributes ||
							Object.keys(validateHeadersLocally()).length > 0
						}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
					>
						<span className="text-xs leading-none font-medium text-white">
							Next
						</span>
					</Button>
				</div>
			</div>

			{/* Add Attribute Dialog */}
			<Dialog
				open={showAddAttributeDialog}
				onOpenChange={setShowAddAttributeDialog}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Create New Field</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<label className="mb-1 block text-sm font-medium text-gray-700">
								Field Label
							</label>
							<InputText
								value={newAttributeData.label}
								onChange={(e) =>
									setNewAttributeData((prev) => ({
										...prev,
										label: e.target.value,
									}))
								}
								placeholder="Enter field label"
							/>
						</div>
						<div>
							<label className="mb-1 block text-sm font-medium text-gray-700">
								Field Type
							</label>
							<Select
								value={newAttributeData.type}
								onValueChange={(value) =>
									setNewAttributeData((prev) => ({
										...prev,
										type: value as
											| "text"
											| "email"
											| "phone"
											| "number"
											| "date"
											| "select",
									}))
								}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="text">Text</SelectItem>
									<SelectItem value="email">Email</SelectItem>
									<SelectItem value="phone">Phone</SelectItem>
									<SelectItem value="number">
										Number
									</SelectItem>
									<SelectItem value="date">Date</SelectItem>
									<SelectItem value="select">
										Select
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="flex justify-end space-x-2">
							<Button
								variant="outline"
								onClick={() => setShowAddAttributeDialog(false)}
							>
								Cancel
							</Button>
							<Button onClick={handleAddAttribute}>
								Create Field
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
