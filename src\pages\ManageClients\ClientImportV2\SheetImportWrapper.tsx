import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>Left, ChevronLeft, ChevronRight } from "lucide-react";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import SheetAttributeMapStepView from "./SheetAttributeMapStepView";
import DataValidationStepView from "./DataValidationStepView";
import type { CSVData, Sheet, SystemField, SheetHeader } from "./types";

// Utility function to generate random IDs
const generateRandomId = (): string => {
	return `id_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`;
};

export type SheetStep = "MATCHING" | "VALIDATION" | "IMPORT";

interface SheetImportWrapperProps {
	csvData: CSVData;
	sheets: Sheet[];
	currentSheet: Sheet;
	attributes: SystemField[];
	setAttributes: (attributes: SystemField[]) => void;
	onBack: () => void;
	onSheetChange: (sheet: Sheet) => void;
}

export default function SheetImportWrapper({
	csvData,
	sheets,
	currentSheet,
	attributes,
	setAttributes,
	onBack,
	onSheetChange,
}: SheetImportWrapperProps) {
	const [currentStep, setCurrentStep] = useState<SheetStep>("MATCHING");
	const [currentSheetIndex, setCurrentSheetIndex] = useState(0);
	const [sheetHeaders, setSheetHeaders] = useState<SheetHeader[]>([]);

	// Update current sheet index when currentSheet changes
	useEffect(() => {
		const index = sheets.findIndex((sheet) => sheet.id === currentSheet.id);
		if (index !== -1) {
			setCurrentSheetIndex(index);
		}
	}, [currentSheet, sheets]);

	const handlePrevSheet = () => {
		if (currentSheetIndex > 0) {
			const prevSheet = sheets[currentSheetIndex - 1];
			setCurrentSheetIndex(currentSheetIndex - 1);
			onSheetChange(prevSheet);
		}
	};

	const handleNextSheet = () => {
		if (currentSheetIndex < sheets.length - 1) {
			const nextSheet = sheets[currentSheetIndex + 1];
			setCurrentSheetIndex(currentSheetIndex + 1);
			onSheetChange(nextSheet);
		}
	};

	const handleSheetSelect = (sheetId: string) => {
		const sheet = sheets.find((s) => s.id === sheetId);
		if (sheet) {
			const index = sheets.findIndex((s) => s.id === sheetId);
			setCurrentSheetIndex(index);
			onSheetChange(sheet);
		}
	};

	const handleMatchingComplete = (headers?: SheetHeader[]) => {
		if (headers) {
			setSheetHeaders(headers);
		}
		setCurrentStep("VALIDATION");
	};

	// Initialize sheet headers when current sheet changes
	useEffect(() => {
		if (currentSheet && csvData.headers[currentSheet.label]) {
			// Create default headers if none exist
			if (sheetHeaders.length === 0) {
				const defaultHeaders: SheetHeader[] = csvData.headers[
					currentSheet.label
				].map((headerLabel) => ({
					tempId: generateRandomId(),
					sheetId: currentSheet.id,
					labelOnFile: headerLabel,
					systemFieldKey: null,
					errors: [],
				}));
				setSheetHeaders(defaultHeaders);
				console.log("Created default headers:", defaultHeaders);
			}
		}
	}, [currentSheet, csvData.headers, sheetHeaders.length]);

	const renderCurrentStep = () => {
		// Safety check for currentSheet
		if (!currentSheet) {
			return (
				<div className="flex min-h-[400px] flex-col items-center justify-center space-y-4">
					<h2 className="text-2xl font-semibold">
						No Sheet Selected
					</h2>
					<p className="text-gray-600">
						Please ensure the file contains valid data.
					</p>
				</div>
			);
		}

		switch (currentStep) {
			case "MATCHING":
				return (
					<SheetAttributeMapStepView
						csvData={csvData}
						currentSheet={currentSheet}
						attributes={attributes}
						setAttributes={setAttributes}
						onBack={onBack}
						onNext={(headers) => handleMatchingComplete(headers)}
					/>
				);
			case "VALIDATION":
				return (
					<DataValidationStepView
						csvData={csvData}
						currentSheet={currentSheet}
						sheetHeaders={sheetHeaders}
						attributes={attributes}
						onBack={() => setCurrentStep("MATCHING")}
						onNext={() => setCurrentStep("IMPORT")}
					/>
				);
			case "IMPORT":
				return (
					<div className="flex min-h-[400px] flex-col items-center justify-center space-y-4">
						<h2 className="text-2xl font-semibold">Import Step</h2>
						<p className="text-gray-600">
							Import functionality will be implemented here.
						</p>
						<Button onClick={() => setCurrentStep("VALIDATION")}>
							<ArrowLeft className="mr-2 h-4 w-4" />
							Back to Validation
						</Button>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{currentSheet && (
				<SelectSheetInfoCard
					onNext={() => setCurrentStep("VALIDATION")}
					selectedSheet={currentSheet.label}
					sheets={sheets.map((sheet) => sheet.label)}
					onSheetSelect={(sheetLabel) => {
						const sheet = sheets.find(
							(s) => s.label === sheetLabel
						);
						if (sheet) {
							onSheetChange(sheet);
						}
					}}
				/>
			)}

			{/* Current Step Content */}
			{renderCurrentStep()}
		</div>
	);
}
