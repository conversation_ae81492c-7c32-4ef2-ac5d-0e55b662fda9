// System Field structure
export interface SystemField {
	key: string;
	label: string;
	is_system_field: boolean;
	is_validator?: boolean;
	is_required: boolean;
	type: "text" | "email" | "phone" | "number" | "date" | "select";
	options?: string[]; // for select type
}

export interface Sheet {
	id: string;
	label: string;
}

// Mapping step types
export interface BaseSheetHeader {
	tempId: string; // should be generated
	sheetId: string;
	labelOnFile: string | null;
	systemFieldKey: string | null;
}

export interface SheetHeader extends BaseSheetHeader {
	labelOnFile: string | null;
	systemFieldKey: string | null;
	systemField?: SystemField; // populated after mapping (also type will be from this)
	errors: HeaderValidationError[];
	newFieldLabel?: string;
	newFieldType?: "text" | "email" | "phone" | "number" | "date" | "select";
	isValidator?: boolean;
}

export interface HeaderValidationError {
	type:
		| "invalid_label"
		| "missing_system_field"
		| "empty_label"
		| "invalid_system_key";
	headerId: string;
	message?: string;
}

export interface GroupedHeaderError {
	type:
		| "duplicate_label"
		| "duplicated_system_field_key"
		| "general_validation"; // general_validation eg. first_name should be in data (header)
	affectedFields?: string[]; // for combined field validation
	message: string;
}

export interface SheetHeaderData {
	sheetId: string;
	headers: SheetHeader[];
}

export interface ValidationResponse {
	is_valid: boolean;
	sheet_errors?: Record<string, GroupedHeaderError[]>;
	header_errors?: Record<string, HeaderValidationError[]>;
}

// Data validation types
export interface DataValidationRequest {
	sheetId: string;
	data: Array<Record<string, string>>; // array of rows, each row has systemFieldKey -> value
}

export interface DataValidationError {
	type:
		| "required_field_missing"
		| "invalid_type"
		| "invalid_option"
		| "duplicate_client"
		| "general_validation";
	row_index: number;
	system_field_key?: string;
	message: string;
}

export interface ApiResponse<T> {
	success: boolean;
	data: T;
	message: string;
}

export interface DataValidation {
	is_valid: boolean;
	error_type: "Header" | "Data";
	header_errors?: Record<string, HeaderValidationError[]>;
	data_errors?: DataValidationError[];
	message?: string;
}

export interface DataValidationResponse extends ApiResponse<DataValidation> {}

export interface AddAttributeRequest {
	labelOnFile: string;
	label: string;
	type: "text" | "email" | "phone" | "number" | "date" | "select";
	options: string[];
}

export type ImportStep = "UPLOAD" | "MATCH" | "IMPORT";

export interface CSVData {
	sheets: Sheet[];
	selectedSheet: string;
	headers: Record<string, string[]>; // sheetId -> headers[]
	data: Record<string, Array<Record<string, string>>>; // sheetId -> rows[]
}

export interface CommitDataResponse {
	success: boolean;
	data: {
		created_count: number;
		updated_count: number;
		total_processed: number;
		errors: Array<{
			row_index: number;
			message: string;
		}>;
	};
	message: string;
}
