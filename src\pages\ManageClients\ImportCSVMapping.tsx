import { <PERSON><PERSON> } from "@/components/ui/button";
import { Info, Refresh<PERSON>w, CheckCircle, AlertCircle } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import { InputText } from "@/components/common/InputText/InputText";
import { useState, useEffect, useMemo } from "react";
import {
	getFileHeaders,
	getFileSheets,
	autoMatchColumns,
	type FieldOption,
} from "@/utils/fileParser";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import type { BusinessAttribute } from "@/types/businessAttributes";
import {
	businessAttributesApi,
	type BulkUpdateAttributeData,
} from "@/lib/api/businessAttributes";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { clientImportV2Api } from "@/lib/api/clientImportV2";
import type {
	ValidationResponse,
	SystemField,
	AddAttributeRequest,
} from "@/pages/ManageClients/ClientImportV2/types";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";

interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
	isAutoMatched?: boolean;
	matchConfidence?: number;
	isValidator?: boolean;
}

interface MigraniumField {
	key: string;
	label: string;
	type: string;
}

interface CustomField {
	key: string;
	label: string;
	type: string;
}

interface ImportCSVMappingProps {
	uploadedFile: File | null;
	onBack: () => void;
	onNext: () => void;
	onTest: () => void;
	onMappingComplete?: (mappingData: {
		mappingRows: MappingRow[];
		selectedSheet: string;
		availableSheets: string[];
		clearStoredCustomFields: () => void;
	}) => void;
}

export default function ImportCSVMapping({
	uploadedFile,
	onBack,
	onNext,
	onTest,
	onMappingComplete,
}: ImportCSVMappingProps) {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();

	// Use the client import V2 specific attributes endpoint
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useQuery({
			queryKey: ["clientImportV2Attributes", organizationId],
			queryFn: () => clientImportV2Api.getAttributes(organizationId || 0),
			enabled: !!organizationId,
		});

	// Memoize businessAttributes to prevent infinite re-renders
	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];
		console.log("ImportCSVMapping - Client attributes loaded:", {
			businessAttributesData,
			attributes,
			isLoading: isLoadingAttributes,
		});

		// Transform the SystemField structure to match the expected BusinessAttribute interface
		const transformedAttributes = attributes.map((attr: any) => {
			return {
				id: attr.id || null,
				key: attr.key,
				label: attr.label,
				type: attr.type,
				options: attr.options || [],
				is_required: attr.is_required || false,
				is_basic_attribute: attr.is_basic_attribute || false,
				is_system_field: attr.is_system_field || false,
				show_in_list: attr.show_in_list || false,
				import_column_name: attr.label, // Use label as import column name
				field_config_id: attr.field_config_id || null,
				emr_sync_id: attr.emr_sync_id || null,
				is_validator: attr.is_validator || false,
				business_id: attr.business_id || null,
			};
		});

		// Deduplicate attributes by key - prefer system fields over custom fields
		// This handles cases where the API returns multiple entries with the same key
		const deduplicatedAttributes = transformedAttributes.reduce(
			(acc: BusinessAttribute[], current: BusinessAttribute) => {
				const existingIndex = acc.findIndex(
					(attr) => attr.key === current.key
				);

				if (existingIndex === -1) {
					// No duplicate found, add the attribute
					acc.push(current);
				} else {
					// Duplicate found, prefer system fields over custom fields
					const existing = acc[existingIndex];
					if (current.is_system_field && !existing.is_system_field) {
						acc[existingIndex] = current;
					}
				}
				return acc;
			},
			[]
		);

		return deduplicatedAttributes;
	}, [businessAttributesData, isLoadingAttributes]);

	// Helper function to get storage key based on file name
	const getStorageKey = (fileName: string) =>
		`csv_custom_fields_${fileName.replace(/[^a-zA-Z0-9]/g, "_")}`;

	const [mappingRows, setMappingRows] = useState<MappingRow[]>([]);
	const [isLoadingHeaders, setIsLoadingHeaders] = useState(false);
	const [validationResult, setValidationResult] =
		useState<ValidationResponse | null>(null);
	const [isValidating, setIsValidating] = useState(false);
	const [showSuccessMessage, setShowSuccessMessage] = useState(false);
	const [showAddAttributeDialog, setShowAddAttributeDialog] = useState(false);
	const [creatingForRowId, setCreatingForRowId] = useState<number | null>(
		null
	);
	const [newAttributeData, setNewAttributeData] =
		useState<AddAttributeRequest>({
			labelOnFile: "",
			label: "",
			type: "text",
			options: [],
		});

	// Initialize custom fields from localStorage if available
	const [customFields, setCustomFields] = useState<CustomField[]>(() => {
		if (uploadedFile) {
			const stored = localStorage.getItem(
				getStorageKey(uploadedFile.name)
			);
			if (stored) {
				try {
					const parsedFields = JSON.parse(stored);
					console.log(
						"Restored custom fields from localStorage:",
						parsedFields
					);
					return parsedFields;
				} catch (error) {
					console.error("Error parsing stored custom fields:", error);
				}
			}
		}
		return [];
	});

	const [availableSheets, setAvailableSheets] = useState<string[]>([]);
	const [selectedSheet, setSelectedSheet] = useState<string>("");

	// Effect to save custom fields to localStorage whenever they change
	useEffect(() => {
		if (uploadedFile && customFields.length > 0) {
			const storageKey = getStorageKey(uploadedFile.name);
			localStorage.setItem(storageKey, JSON.stringify(customFields));
			console.log("Saved custom fields to localStorage:", customFields);
		}
	}, [customFields, uploadedFile]);

	// Helper function to update custom fields and persist to localStorage
	const updateCustomFields = (newFields: CustomField[]) => {
		setCustomFields(newFields);
		if (uploadedFile) {
			const storageKey = getStorageKey(uploadedFile.name);
			localStorage.setItem(storageKey, JSON.stringify(newFields));
		}
	};

	// Function to clear stored custom fields (call this on successful import)
	const clearStoredCustomFields = () => {
		if (uploadedFile) {
			const storageKey = getStorageKey(uploadedFile.name);
			localStorage.removeItem(storageKey);
			console.log("Cleared stored custom fields for:", uploadedFile.name);
		}
	};

	const fieldTypes = [
		{ value: "text", label: "Text" },
		{ value: "number", label: "Number" },
		{ value: "email", label: "Email" },
		{ value: "phone", label: "Phone" },
		{ value: "date", label: "Date" },
		{ value: "checkbox", label: "Checkbox" },
		{ value: "textarea", label: "Textarea" },
	];

	const availableMigraniumFields: MigraniumField[] = [
		// Use attributes directly from the API response - no hardcoding
		...businessAttributes.map((attr) => ({
			key: attr.key,
			label: attr.label,
			type: attr.type,
		})),
		...customFields.map((field) => ({
			key: field.key,
			label: field.label,
			type: field.type,
		})),
	];

	useEffect(() => {
		const loadFileSheets = async () => {
			if (!uploadedFile) {
				setAvailableSheets([]);
				setSelectedSheet("");
				return;
			}

			try {
				const sheets = await getFileSheets(uploadedFile);
				setAvailableSheets(sheets);
				if (sheets.length > 0) {
					setSelectedSheet(sheets[0]);
				} else {
					setSelectedSheet("");
				}
			} catch (error) {
				console.error("Error loading file sheets:", error);
				setAvailableSheets([]);
				setSelectedSheet("");
			}
		};

		loadFileSheets();
	}, [uploadedFile]);

	useEffect(() => {
		const loadFileHeaders = async () => {
			if (!uploadedFile || isLoadingAttributes) return;

			setIsLoadingHeaders(true);
			try {
				const headers = await getFileHeaders(
					uploadedFile,
					selectedSheet || undefined
				);

				if (headers.length > 0) {
					const availableFields: FieldOption[] = [
						// Use attributes directly from the API response - no hardcoding
						...businessAttributes.map((attr) => ({
							key: attr.key,
							label: attr.label,
							type: attr.type,
						})),
						...customFields.map((field) => ({
							key: field.key,
							label: field.label,
							type: field.type,
						})),
					];

					console.log(
						"Available fields for auto-matching:",
						availableFields
					);
					console.log("CSV headers:", headers);

					const autoMatchResults = autoMatchColumns(
						headers,
						availableFields
					);

					console.log("Auto-match results:", autoMatchResults);

					const newMappingRows: MappingRow[] = autoMatchResults.map(
						(result, index) => {
							// Find the business attribute to get the validator state
							const businessAttribute = businessAttributes.find(
								(attr) => attr.key === result.matchedField?.key
							);

							return {
								id: index + 1,
								excel: result.csvHeader,
								migranium: result.matchedField?.key || "",
								type: result.matchedField?.type || "text",
								isCreatingNew: false,
								newFieldLabel: "",
								isAutoMatched: !!result.matchedField,
								matchConfidence: result.confidence,
								isValidator:
									businessAttribute?.is_validator || false,
							};
						}
					);
					setMappingRows(newMappingRows);
				}
			} catch (error) {
				console.error("Error loading file headers:", error);
			} finally {
				setIsLoadingHeaders(false);
			}
		};

		loadFileHeaders();
	}, [
		uploadedFile,
		selectedSheet,
		businessAttributes,
		customFields,
		isLoadingAttributes,
	]);

	const handleExcelColumnChange = (id: number, value: string) => {
		setMappingRows((prev) =>
			prev.map((row) => (row.id === id ? { ...row, excel: value } : row))
		);
	};

	const handleMigraniumFieldChange = (id: number, value: string) => {
		if (value === "CREATE_NEW") {
			setCreatingForRowId(id);
			const currentRow = mappingRows.find((row) => row.id === id);
			setNewAttributeData((prev) => ({
				...prev,
				labelOnFile: currentRow?.excel || "",
			}));
			setShowAddAttributeDialog(true);
			return;
		} else {
			const selectedField = availableMigraniumFields.find(
				(field) => field.key === value
			);
			// Find the business attribute to get the validator state
			const businessAttribute = businessAttributes.find(
				(attr) => attr.key === value
			);

			setMappingRows((prev) =>
				prev.map((row) =>
					row.id === id
						? {
								...row,
								migranium: value,
								type: selectedField?.type || "text",
								isCreatingNew: false,
								newFieldLabel: "",
								isAutoMatched: false,
								matchConfidence: 0,
								isValidator:
									businessAttribute?.is_validator || false,
							}
						: row
				)
			);
		}
	};

	const handleNewFieldLabelChange = (id: number, label: string) => {
		setMappingRows((prev) =>
			prev.map((row) =>
				row.id === id ? { ...row, newFieldLabel: label } : row
			)
		);
	};

	const handleNewFieldTypeChange = (id: number, type: string) => {
		setMappingRows((prev) =>
			prev.map((row) => {
				if (row.id === id) {
					if (row.migranium && row.migranium.startsWith("custom_")) {
						setCustomFields((prevCustomFields) =>
							prevCustomFields.map((field) =>
								field.key === row.migranium
									? { ...field, type }
									: field
							)
						);
					}
					return { ...row, type };
				}
				return row;
			})
		);
	};

	// Function to check if a field label already exists
	const isFieldLabelExists = (label: string): boolean => {
		const trimmedLabel = label.trim().toLowerCase();

		// Check against business attributes from API
		const businessAttributeLabels = businessAttributes.map((attr) =>
			attr.label.toLowerCase()
		);

		if (businessAttributeLabels.includes(trimmedLabel)) {
			return true;
		}

		// Check against existing custom fields
		const customFieldLabels = customFields.map((field) =>
			field.label.toLowerCase()
		);

		if (customFieldLabels.includes(trimmedLabel)) {
			return true;
		}

		return false;
	};

	const handleAddAttribute = async () => {
		try {
			const response = await clientImportV2Api.addAttribute(
				newAttributeData,
				organizationId || 0
			);

			// Invalidate and refetch the attributes query
			await queryClient.invalidateQueries({
				queryKey: ["clientImportV2Attributes", organizationId],
			});

			// If we have the new attribute data from the response, add it to the current attributes
			if (response?.data) {
				const newAttribute = {
					key: response.data.key || `custom_${Date.now()}`,
					label: newAttributeData.label,
					type: newAttributeData.type,
				};

				// Add to custom fields for immediate use
				updateCustomFields([...customFields, newAttribute]);

				// Automatically select the new field in the row that triggered the creation
				if (creatingForRowId) {
					setMappingRows((prev) =>
						prev.map((row) => {
							if (row.id === creatingForRowId) {
								return {
									...row,
									migranium: newAttribute.key,
									type: newAttribute.type,
									isCreatingNew: false,
									newFieldLabel: "",
									isAutoMatched: false,
									matchConfidence: 0,
									isValidator: false,
								};
							}
							return row;
						})
					);
				}
			}

			setShowAddAttributeDialog(false);
			setCreatingForRowId(null);
			setNewAttributeData({
				labelOnFile: "",
				label: "",
				type: "text",
				options: [],
			});
		} catch (error) {
			console.error("Failed to create new field:", error);
			alert("Failed to create new field. Please try again.");
		}
	};

	const handleCreateNewField = async (id: number) => {
		const row = mappingRows.find((r) => r.id === id);
		if (!row || !row.newFieldLabel) return;

		// Check if the field label already exists
		if (isFieldLabelExists(row.newFieldLabel)) {
			alert(
				`A field with the title "${row.newFieldLabel}" already exists. Please choose a different title.`
			);
			return;
		}

		try {
			// Use the new API to create the attribute
			const attributeData: AddAttributeRequest = {
				labelOnFile: row.excel,
				label: row.newFieldLabel,
				type: row.type as
					| "text"
					| "email"
					| "phone"
					| "number"
					| "date"
					| "select",
				options: [],
			};

			const response = await clientImportV2Api.addAttribute(
				attributeData,
				organizationId || 0
			);

			// Invalidate and refetch the attributes query
			await queryClient.invalidateQueries({
				queryKey: ["clientImportV2Attributes", organizationId],
			});

			// If we have the new attribute data from the response, add it to the current attributes
			if (response?.data) {
				const newAttribute = {
					key: response.data.key || `custom_${Date.now()}`,
					label: row.newFieldLabel,
					type: row.type,
				};

				// Add to custom fields for immediate use
				updateCustomFields([...customFields, newAttribute]);

				// Automatically select the new field in the row
				setMappingRows((prev) =>
					prev.map((r) =>
						r.id === id
							? {
									...r,
									migranium: newAttribute.key,
									type: newAttribute.type,
									isCreatingNew: false,
									newFieldLabel: "",
									isAutoMatched: false,
									matchConfidence: 0,
									isValidator: false,
								}
							: r
					)
				);
			}
		} catch (error) {
			console.error("Failed to add attribute:", error);
			alert("Failed to create new field. Please try again.");
		}
	};

	const getAvailableOptions = (currentRowId: number) => {
		const currentRow = mappingRows.find((row) => row.id === currentRowId);
		const selectedValues = mappingRows
			.filter((row) => row.id !== currentRowId && row.migranium)
			.map((row) => row.migranium);

		let availableFields = availableMigraniumFields.filter(
			(field) => !selectedValues.includes(field.key)
		);
		if (
			currentRow?.migranium &&
			!availableFields.find((f) => f.key === currentRow.migranium)
		) {
			const currentField = availableMigraniumFields.find(
				(f) => f.key === currentRow.migranium
			);
			if (currentField) {
				availableFields = [currentField, ...availableFields];
			}
		}
		return [
			...availableFields,
			{ key: "CREATE_NEW", label: "Create New Field", type: "text" },
		];
	};

	const handleRefreshHeaders = async () => {
		if (!uploadedFile) return;

		setIsLoadingHeaders(true);
		try {
			const headers = await getFileHeaders(
				uploadedFile,
				selectedSheet || undefined
			);

			if (headers.length > 0) {
				const availableFields: FieldOption[] = [
					// Use attributes directly from the API response - no hardcoding
					...businessAttributes.map((attr) => ({
						key: attr.key,
						label: attr.label,
						type: attr.type,
					})),
					...customFields.map((field) => ({
						key: field.key,
						label: field.label,
						type: field.type,
					})),
				];
				const autoMatchResults = autoMatchColumns(
					headers,
					availableFields
				);
				const newMappingRows: MappingRow[] = autoMatchResults.map(
					(result, index) => {
						// Find the business attribute to get the validator state
						const businessAttribute = businessAttributes.find(
							(attr) => attr.key === result.matchedField?.key
						);

						return {
							id: index + 1,
							excel: result.csvHeader,
							migranium: result.matchedField?.key || "",
							type: result.matchedField?.type || "text",
							isCreatingNew: false,
							newFieldLabel: "",
							isAutoMatched: !!result.matchedField,
							matchConfidence: result.confidence,
							isValidator:
								businessAttribute?.is_validator || false,
						};
					}
				);
				setMappingRows(newMappingRows);
			}
		} catch (error) {
			console.error("Error refreshing file headers:", error);
		} finally {
			setIsLoadingHeaders(false);
		}
	};

	const handleSheetSelect = (sheet: string) => {
		setSelectedSheet(sheet);
	};

	const handleValidatorToggle = (id: number, checked: boolean) => {
		setMappingRows((prev) => {
			return prev.map((row) =>
				row.id === id ? { ...row, isValidator: checked } : row
			);
		});
	};

	const getValidatorCount = () => {
		return mappingRows.filter((row) => row.isValidator).length;
	};

	const getValidationErrorMessage = (
		validationResult: ValidationResponse
	): string => {
		if (validationResult.is_valid) return "No errors";

		// Collect all error messages
		const errorMessages: string[] = [];

		// Add sheet-level errors
		if (validationResult.sheet_errors) {
			Object.values(validationResult.sheet_errors).forEach((errors) => {
				errors.forEach((error) => {
					errorMessages.push(error.message);
				});
			});
		}

		// Add header-level errors
		if (validationResult.header_errors) {
			Object.values(validationResult.header_errors).forEach((errors) => {
				errors.forEach((error) => {
					if (error.message) {
						errorMessages.push(error.message);
					}
				});
			});
		}

		return errorMessages.length > 0
			? errorMessages.join("; ")
			: "Unknown validation error";
	};

	const sendMappingDataToBackend = async (
		mappingData: BulkUpdateAttributeData[]
	) => {
		try {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}

			const response = await businessAttributesApi.bulkUpdateAttributes(
				mappingData,
				organizationId
			);
			return response;
		} catch (error: any) {
			if (error.response?.status === 404) {
				return {
					success: true,
					message: "Endpoint not implemented yet",
				};
			}
			throw error;
		}
	};

	const getSystemFieldKeyFromMapping = (
		item: BulkUpdateAttributeData
	): string | null => {
		// If it has an ID, it's an existing business attribute
		if (item.id) {
			const businessAttribute = businessAttributes.find(
				(attr) => attr.id === item.id
			);
			return businessAttribute?.key || null;
		}

		// If it's a custom field, find it by label
		const customField = customFields.find(
			(field) => field.label === item.label
		);
		return customField?.key || null;
	};

	const validateMappingData = async (
		mappingData?: BulkUpdateAttributeData[]
	) => {
		try {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}

			// Use mappingRows if no specific data provided
			const dataToValidate =
				mappingData || transformMappingDataForBackend();

			// Transform the data to match the new API format
			const requestData = {
				sheetId: selectedSheet || "Sheet1", // Use selected sheet or default
				headers: dataToValidate.map((item, index) => ({
					tempId: `temp_${index}`,
					labelOnFile: item.import_column_name,
					systemFieldKey: getSystemFieldKeyFromMapping(item),
					is_validator: item.is_validator || false,
				})),
			};

			const response = await clientImportV2Api.validateAttributeMapping(
				requestData,
				organizationId
			);
			return response;
		} catch (error: any) {
			throw error;
		}
	};

	const handleTestValidation = async () => {
		try {
			const validatorCount = getValidatorCount();
			if (validatorCount === 1) {
				alert(
					`Validation requires either 0 validators (no validation) or 2+ validators. Currently ${validatorCount} selected.`
				);
				return;
			}

			const backendMappingData = transformMappingDataForBackend();

			if (backendMappingData.length === 0) {
				alert(
					"No mapping data to validate. Please map at least one column."
				);
				return;
			}

			setIsValidating(true);
			setValidationResult(null);
			setShowSuccessMessage(false);

			console.log("Validating mapping data:", backendMappingData);

			const validationResult =
				await validateMappingData(backendMappingData);

			if (validationResult.is_valid) {
				setShowSuccessMessage(true);
				setValidationResult(validationResult);
				alert(
					"✅ Validation successful! The mapping data is correct and ready to be processed."
				);
			} else {
				setValidationResult(validationResult);
				setShowSuccessMessage(false);
				const errorMessage =
					getValidationErrorMessage(validationResult);
				alert(`❌ Validation failed: ${errorMessage}`);
			}
		} catch (error: any) {
			console.error("Validation error:", error);
			setShowSuccessMessage(false);
			const errorMessage =
				error.response?.data?.message ||
				error.message ||
				"Validation failed";
			alert(`❌ Validation error: ${errorMessage}`);
		} finally {
			setIsValidating(false);
		}
	};

	const handleTestRowValidation = async (row: MappingRow) => {
		try {
			if (!row.migranium || row.migranium.trim() === "") {
				alert("Please select a Migranium field first.");
				return;
			}

			let titleOnMigranium = row.migranium;
			const businessAttribute = businessAttributes.find(
				(attr) => attr.key === row.migranium
			);
			if (businessAttribute) {
				titleOnMigranium = businessAttribute.label;
			} else if (row.migranium.startsWith("custom_")) {
				const customField = customFields.find(
					(field) => field.key === row.migranium
				);
				titleOnMigranium =
					customField?.label || row.newFieldLabel || row.migranium;
			}

			const rowData = {
				id: businessAttribute?.id || null,
				label: titleOnMigranium,
				type: row.type,
				is_validator: row.isValidator || false,
				import_column_name: row.excel,
			};

			console.log("Validating row data:", rowData);

			const validationResult = await validateMappingData([rowData]);

			if (validationResult.is_valid) {
				alert("✅ Row validation successful!");
			} else {
				const errorMessage =
					getValidationErrorMessage(validationResult);
				alert(`❌ Row validation failed: ${errorMessage}`);
			}
		} catch (error: any) {
			console.error("Row validation error:", error);
			const errorMessage =
				error.response?.data?.message ||
				error.message ||
				"Validation failed";
			alert(`❌ Row validation error: ${errorMessage}`);
		}
	};

	const transformMappingDataForBackend = (): BulkUpdateAttributeData[] => {
		return mappingRows
			.filter((row) => row.migranium && row.migranium.trim() !== "")
			.map((row) => {
				let titleOnMigranium = row.migranium;
				const businessAttribute = businessAttributes.find(
					(attr) => attr.key === row.migranium
				);
				if (businessAttribute) {
					titleOnMigranium = businessAttribute.label;
				} else if (row.migranium.startsWith("custom_")) {
					const customField = customFields.find(
						(field) => field.key === row.migranium
					);
					titleOnMigranium =
						customField?.label ||
						row.newFieldLabel ||
						row.migranium;
				}

				return {
					id: businessAttribute?.id || null, // Include ID for existing attributes
					label: titleOnMigranium,
					type: row.type,
					is_validator: row.isValidator || false,
					import_column_name: row.excel,
				};
			});
	};

	const handleNext = async () => {
		const validatorCount = getValidatorCount();
		if (validatorCount === 1) {
			alert(
				`Validation requires either 0 validators (no validation) or 2+ validators. Currently ${validatorCount} selected.`
			);
			return;
		}

		try {
			const backendMappingData = transformMappingDataForBackend();
			await sendMappingDataToBackend(backendMappingData);

			if (onMappingComplete) {
				onMappingComplete({
					mappingRows,
					selectedSheet,
					availableSheets,
					clearStoredCustomFields,
				});
			}
			onNext();
		} catch (error) {
			console.error("Failed to send mapping data:", error);
			alert("Failed to save mapping data. Please try again.");
		}
	};
	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{/* Success Message */}
			{showSuccessMessage && (
				<div className="rounded-lg border border-green-200 bg-green-50 p-4">
					<div className="flex items-center">
						<CheckCircle className="mr-2 h-5 w-5 text-green-500" />
						<span className="font-medium text-green-700">
							🎉 Validation successful! All mappings are correct.
							You can proceed to the next step.
						</span>
					</div>
				</div>
			)}

			{/* Error Message */}
			{validationResult && !validationResult.is_valid && (
				<div className="rounded-lg border border-red-200 bg-red-50 p-4">
					<div className="flex items-center">
						<AlertCircle className="mr-2 h-5 w-5 text-red-500" />
						<span className="text-red-700">
							❌ Please fix the mapping errors below before
							proceeding.
						</span>
					</div>
				</div>
			)}

			<SelectSheetInfoCard
				onNext={onNext}
				selectedSheet={selectedSheet || "Select a sheet"}
				sheets={availableSheets}
				onSheetSelect={handleSheetSelect}
			/>
			<div className="flex flex-col items-start justify-start self-stretch rounded-2xl p-2">
				<div className="inline-flex items-center justify-start gap-3 self-stretch pb-2">
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Column Titles from Excel
						</div>
						{uploadedFile && (
							<Button
								variant="ghost"
								size="sm"
								onClick={handleRefreshHeaders}
								disabled={isLoadingHeaders}
								className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
							>
								<RefreshCw
									className={`h-3 w-3 ${isLoadingHeaders ? "animate-spin" : ""}`}
								/>
							</Button>
						)}
					</div>
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Titles on Migranium
						</div>
					</div>
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Title Type
						</div>
					</div>
					<div className="inline-flex w-28 min-w-20 flex-col items-start justify-center gap-0.5">
						<div className="inline-flex items-center justify-start gap-0.5">
							<div className="justify-center text-xs leading-none font-medium text-gray-500">
								Validator
							</div>
							<Info className="h-3 w-3 text-gray-500" />
						</div>
						<div className="w-24 justify-start text-[8px] leading-3 font-normal text-gray-400">
							({getValidatorCount()} selected, 0 or 2+)
						</div>
					</div>
					<div className="flex w-20 min-w-20 items-center justify-start">
						<div className="justify-center text-xs leading-none font-medium text-gray-500">
							Test
						</div>
					</div>
				</div>

				{mappingRows.length === 0 ? (
					<div className="flex items-center justify-center py-8 text-gray-500">
						<div className="text-center">
							<p className="text-sm">
								{uploadedFile
									? "Loading CSV headers..."
									: "Upload a CSV file to start mapping columns"}
							</p>
						</div>
					</div>
				) : (
					mappingRows.map((row) => (
						<div
							key={row.id}
							className="inline-flex items-center justify-start gap-3 self-stretch border-t border-gray-200 py-2"
						>
							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								<div className="flex flex-col items-start justify-start gap-2 self-stretch">
									<InputText
										value={row.excel}
										onChange={(e) =>
											handleExcelColumnChange(
												row.id,
												e.target.value
											)
										}
										size="sm"
										className="h-9 border-gray-300 bg-gray-50/35 text-xs font-normal text-gray-900 focus:outline-none"
										placeholder={
											isLoadingHeaders
												? "Loading headers..."
												: "Enter column title"
										}
										disabled={true}
									/>
								</div>
							</div>

							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								{row.isCreatingNew ? (
									<div className="flex w-full flex-col gap-1">
										<div className="flex w-full items-start gap-1">
											<Select
												value="CREATE_NEW"
												onValueChange={(value) => {
													if (
														value !== "CREATE_NEW"
													) {
														handleMigraniumFieldChange(
															row.id,
															value as string
														);
													}
												}}
												disabled={isLoadingAttributes}
											>
												<SelectTrigger className="h-9 w-28 text-xs">
													<SelectValue placeholder="New" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="CREATE_NEW">
														New
													</SelectItem>
													{getAvailableOptions(row.id)
														.filter(
															(field) =>
																field.key !==
																"CREATE_NEW"
														)
														.map((field) => (
															<SelectItem
																key={field.key}
																value={
																	field.key
																}
															>
																{field.label}
															</SelectItem>
														))}
												</SelectContent>
											</Select>
											<InputText
												value={row.newFieldLabel || ""}
												onChange={(e) =>
													handleNewFieldLabelChange(
														row.id,
														e.target.value
													)
												}
												onKeyDown={(e) => {
													if (
														e.key === "Enter" &&
														row.newFieldLabel?.trim()
													) {
														handleCreateNewField(
															row.id
														);
													}
												}}
												onBlur={() => {
													if (
														row.newFieldLabel?.trim()
													) {
														handleCreateNewField(
															row.id
														);
													}
												}}
												size="sm"
												className={`h-9 flex-1 border-gray-300 bg-white text-xs font-normal text-gray-900 focus:outline-none ${
													row.newFieldLabel &&
													isFieldLabelExists(
														row.newFieldLabel
													)
														? "border-red-300 bg-red-50"
														: ""
												}`}
												placeholder="Enter Title"
											/>
										</div>
										{row.newFieldLabel &&
											isFieldLabelExists(
												row.newFieldLabel
											) && (
												<div className="px-1 text-xs text-red-500">
													This title already exists.
													Please choose a different
													one.
												</div>
											)}
									</div>
								) : (
									<Select
										key={`${row.id}-${row.migranium}-${customFields.length}`}
										value={row.migranium}
										onValueChange={(value) =>
											handleMigraniumFieldChange(
												row.id,
												value as string
											)
										}
										disabled={isLoadingAttributes}
									>
										<SelectTrigger className="h-9 w-full text-xs">
											<SelectValue
												placeholder={
													isLoadingAttributes
														? "Loading fields..."
														: "Select Migranium field"
												}
											/>
										</SelectTrigger>
										<SelectContent>
											{getAvailableOptions(row.id).map(
												(field) => (
													<SelectItem
														key={field.key}
														value={field.key}
													>
														{field.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
								)}
							</div>

							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								{row.isCreatingNew ||
								(row.migranium &&
									row.migranium.startsWith("custom_")) ? (
									<Select
										value={row.type}
										onValueChange={(value) =>
											handleNewFieldTypeChange(
												row.id,
												value as string
											)
										}
									>
										<SelectTrigger className="h-9 w-full text-xs">
											<SelectValue placeholder="Select Type" />
										</SelectTrigger>
										<SelectContent>
											{fieldTypes.map((type) => (
												<SelectItem
													key={type.value}
													value={type.value}
												>
													{type.label}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								) : (
									<div className="inline-flex h-9 items-center justify-start self-stretch overflow-hidden rounded-md bg-gray-50/35 px-3 py-2 outline-1 outline-offset-[-1px] outline-gray-300">
										<div className="flex-1 justify-start text-xs leading-none font-normal text-gray-600 capitalize">
											{row.type || "Select field first"}
										</div>
									</div>
								)}
							</div>

							<div className="flex w-28 items-center justify-start gap-1 overflow-hidden py-2">
								<div className="flex items-start justify-start gap-2">
									<div className="flex items-center justify-start gap-1.5">
										<Switch
											checked={row.isValidator || false}
											onCheckedChange={(checked) =>
												handleValidatorToggle(
													row.id,
													checked
												)
											}
										/>
										<div className="h-5 w-5 justify-center text-xs leading-none font-normal text-gray-500">
											{row.isValidator ? "On" : "Off"}
										</div>
									</div>
								</div>
							</div>

							<div className="flex w-20 items-center justify-start py-2">
								<Button
									onClick={() => handleTestRowValidation(row)}
									variant="outline"
									size="sm"
									className="h-7 px-2 text-xs"
									disabled={
										!row.migranium ||
										row.migranium.trim() === ""
									}
								>
									Test
								</Button>
							</div>
						</div>
					))
				)}
			</div>

			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<Button
					onClick={onBack}
					variant="ghost"
					className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2"
				>
					<span className="text-xs leading-none font-medium text-gray-900">
						Back
					</span>
				</Button>

				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={handleTestValidation}
						variant="outline"
						disabled={isValidating}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-[#005893]"
					>
						{isValidating ? (
							<>
								<div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
								Testing...
							</>
						) : (
							<span className="text-xs leading-none font-medium text-[#005893]">
								Test
							</span>
						)}
					</Button>

					<Button
						onClick={handleNext}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
					>
						<span className="text-xs leading-none font-medium text-white">
							Next
						</span>
					</Button>
				</div>
			</div>

			{/* Add Attribute Dialog */}
			<Dialog
				open={showAddAttributeDialog}
				onOpenChange={setShowAddAttributeDialog}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Create New Field</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<label className="mb-1 block text-sm font-medium text-gray-700">
								Field Label
							</label>
							<InputText
								value={newAttributeData.label}
								onChange={(e) =>
									setNewAttributeData((prev) => ({
										...prev,
										label: e.target.value,
									}))
								}
								placeholder="Enter field label"
							/>
						</div>
						<div>
							<label className="mb-1 block text-sm font-medium text-gray-700">
								Field Type
							</label>
							<Select
								value={newAttributeData.type}
								onValueChange={(value) =>
									setNewAttributeData((prev) => ({
										...prev,
										type: value as
											| "text"
											| "email"
											| "phone"
											| "number"
											| "date"
											| "select",
									}))
								}
							>
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="text">Text</SelectItem>
									<SelectItem value="email">Email</SelectItem>
									<SelectItem value="phone">Phone</SelectItem>
									<SelectItem value="number">
										Number
									</SelectItem>
									<SelectItem value="date">Date</SelectItem>
									<SelectItem value="select">
										Select
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="flex justify-end space-x-2">
							<Button
								variant="outline"
								onClick={() => setShowAddAttributeDialog(false)}
							>
								Cancel
							</Button>
							<Button onClick={handleAddAttribute}>
								Create Field
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
