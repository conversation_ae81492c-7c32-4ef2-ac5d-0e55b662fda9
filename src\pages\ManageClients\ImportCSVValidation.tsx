import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	ChevronDown,
	Trash2,
	MinusCircle,
	AlertCircle,
	CheckCircle,
	RefreshCw,
} from "lucide-react";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import { ImportSuccessCard } from "@/components/common/ImportSuccessCard";
import { parseFile } from "@/utils/fileParser";
import { InputText } from "@/components/common/InputText/InputText";
import { apiClient } from "@/lib/api/clients";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import type { BusinessAttribute } from "@/types/businessAttributes";
import { parseAndValidatePhone } from "@/components/common/InputPhone/utils";
import { businessAttributesApi } from "@/lib/api/businessAttributes";
import { useQuery } from "@tanstack/react-query";

interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
}

interface MappingData {
	mappingRows: MappingRow[];
	selectedSheet: string;
	availableSheets: string[];
	clearStoredCustomFields?: () => void;
}

interface ValidationRow {
	id: number;
	excelCell: string;
	[key: string]: any;
	errors: string[];
	visualErrors: string[];
	wasProblematic: boolean;
	errorMessages?: Record<string, string>; // Field-specific error messages from backend
}

interface ValidationData {
	readyToImport: number;
	missingDataConflicts: number;
	duplicateDataConflicts: number;
	showMissingData: boolean;
	showDuplicateData: boolean;
	validationRows: ValidationRow[];
	discardedMissingDataRows: Set<number>; // Track which rows are discarded
	discardedDuplicateDataRows: Set<number>; // Track which rows are discarded
	backendValidationErrors?: any; // Store backend validation response
	hasEdgeError?: boolean; // Flag for edge cases requiring all rows update
}

interface ImportState {
	isImporting: boolean;
	isSuccess: boolean;
	importedCount: number;
}

// Add new error state interface
interface ErrorState {
	hasError: boolean;
	message: string;
	type: "validation" | "network" | "server" | "unknown";
	canRetry: boolean;
}

interface ImportCSVValidationProps {
	uploadedFile: File | null;
	mappingData: MappingData | null;
	onBack: () => void;
	onTest: () => void;
	onImport: () => void;
	onPopulateData?: () => void;
	onImportAgain?: () => void;
	onDone?: () => void;
	onImportSuccess?: (isSuccessful: boolean) => void;
}

export default function ImportCSVValidation({
	uploadedFile,
	mappingData,
	onBack,
	onTest,
	onImport,
	onPopulateData,
	onImportAgain,
	onDone,
	onImportSuccess,
}: ImportCSVValidationProps) {
	const { organizationId } = useOrganizationContext();

	// Use the client import specific attributes endpoint
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useQuery({
			queryKey: ["clientImportAttributes", organizationId],
			queryFn: () =>
				businessAttributesApi.getAttributesForClientImport(
					organizationId || 0
				),
			enabled: !!organizationId,
		});

	// Deduplicate and memoize business attributes
	const businessAttributes: BusinessAttribute[] = useMemo(() => {
		const attributes = (businessAttributesData as any)?.data || [];

		// Transform the response structure to use keys consistently
		const transformedAttributes = attributes.map((attr: any) => {
			// Use the key from the backend response directly
			const key = attr.key || attr.import_column_name || "";

			return {
				id: attr.id,
				key: key,
				label: attr.label || attr.import_column_name || "",
				type: attr.type,
				options: attr.options || [],
				is_required: attr.is_required || false,
				is_basic_attribute: attr.is_basic_attribute || false,
				is_system_field: attr.is_system_field || false,
				show_in_list: attr.show_in_list || false,
				import_column_name: attr.import_column_name || "",
				field_config_id: attr.field_config_id,
				emr_sync_id: attr.emr_sync_id,
				is_validator: attr.is_validator || false,
				business_id: attr.business_id,
			};
		});

		// Deduplicate attributes by key - prefer system fields over custom fields
		const deduplicatedAttributes = transformedAttributes.reduce(
			(acc: BusinessAttribute[], current: BusinessAttribute) => {
				const existingIndex = acc.findIndex(
					(attr) => attr.key === current.key
				);

				if (existingIndex === -1) {
					acc.push(current);
				} else {
					const existing = acc[existingIndex];
					if (current.is_system_field && !existing.is_system_field) {
						acc[existingIndex] = current;
					}
				}
				return acc;
			},
			[]
		);

		return deduplicatedAttributes;
	}, [businessAttributesData, isLoadingAttributes]);

	const [validationData, setValidationData] = useState<ValidationData>({
		readyToImport: 0,
		missingDataConflicts: 0,
		duplicateDataConflicts: 0,
		showMissingData: true,
		showDuplicateData: false,
		validationRows: [],
		discardedMissingDataRows: new Set(),
		discardedDuplicateDataRows: new Set(),
	});

	const [isValidating, setIsValidating] = useState(false);
	const [validationPassed, setValidationPassed] = useState(false); // Track if validate-import passed
	const [originalFileData, setOriginalFileData] = useState<any[]>([]); // Store original file data

	const [importState, setImportState] = useState<ImportState>({
		isImporting: false,
		isSuccess: false,
		importedCount: 0,
	});

	// Add error state
	const [errorState, setErrorState] = useState<ErrorState>({
		hasError: false,
		message: "",
		type: "unknown",
		canRetry: false,
	});

	// Validation utility functions
	const validateEmail = (email: string): boolean => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email.trim());
	};

	const validatePhoneNumber = (phone: string): boolean => {
		try {
			// Handle common phone number formats from Excel/CSV
			let normalizedPhone = phone.trim();
			console.log(
				"Validating phone number:",
				phone,
				"-> normalized:",
				normalizedPhone
			);

			// First, try to validate the phone number as-is
			let phoneData = parseAndValidatePhone(normalizedPhone);
			console.log("Initial validation result:", {
				original: phone,
				normalized: normalizedPhone,
				isValid: phoneData.isValid,
				isPossible: phoneData.isPossible,
				country: phoneData.country,
				formattedNumber: phoneData.formattedNumber,
			});

			// If it's already valid, return true
			if (phoneData.isValid) {
				return true;
			}

			// If not valid and doesn't start with +, try adding + prefix
			// This handles cases where users enter numbers without the international prefix
			if (!normalizedPhone.startsWith("+")) {
				// Try with +1 prefix (US/Canada)
				const withPlusOne = `+1${normalizedPhone}`;
				phoneData = parseAndValidatePhone(withPlusOne);
				if (phoneData.isValid) {
					return true;
				}

				// Try with + prefix only
				const withPlus = `+${normalizedPhone}`;
				phoneData = parseAndValidatePhone(withPlus);
				if (phoneData.isValid) {
					return true;
				}
			}

			// If still not valid, check if it's a simple 10-digit number
			const digitsOnly = normalizedPhone.replace(/\D/g, "");
			if (digitsOnly.length === 10) {
				// Try with +1 prefix for 10-digit numbers
				const withPlusOne = `+1${digitsOnly}`;
				phoneData = parseAndValidatePhone(withPlusOne);
				if (phoneData.isValid) {
					return true;
				}
			}

			return false;
		} catch (error) {
			console.error("Error validating phone number:", error);
			return false;
		}
	};

	const validateDate = (dateString: string): boolean => {
		const date = new Date(dateString);
		return !isNaN(date.getTime());
	};

	const validateNumber = (numberString: string): boolean => {
		return !isNaN(Number(numberString));
	};

	const validateBoolean = (boolString: string): boolean => {
		const lower = boolString.toLowerCase();
		return ["true", "false", "yes", "no", "1", "0", "y", "n"].includes(
			lower
		);
	};

	const getRequiredFields = (): string[] => {
		return businessAttributes
			.filter((attr) => attr.is_required)
			.map((attr) => attr.key);
	};

	const getFieldValidationType = (fieldKey: string): string => {
		const attribute = businessAttributes.find(
			(attr) => attr.key === fieldKey
		);
		return attribute?.type || "text";
	};

	// Prepare import data in the expected format (each client with import_column_name as key)
	const prepareImportData = () => {
		if (!mappingData || !uploadedFile) {
			return null;
		}

		const mappedFields = mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);

		if (mappedFields.length === 0) {
			return null;
		}

		// Use keys directly for import data - no need for mapping
		const attributeMapping: Record<string, string> = {};
		mappedFields.forEach((field) => {
			// Use the key directly as the import column name
			attributeMapping[field.migranium] = field.migranium;
		});

		// Parse the file and prepare data
		if (!uploadedFile || !mappingData) {
			return Promise.resolve(null);
		}

		return parseFile(uploadedFile, mappingData.selectedSheet || undefined)
			.then((fileResult) => {
				if (fileResult.error || fileResult.data.length === 0) {
					return null;
				}

				const patients = fileResult.data.map((dataRow) => {
					const patient: Record<string, any> = {};

					mappedFields.forEach((mappingRow) => {
						const excelColumnIndex = mappingRow.id - 1;
						const cellValue = dataRow[excelColumnIndex];
						const fieldKey = mappingRow.migranium;
						const importColumnName = attributeMapping[fieldKey];

						if (importColumnName) {
							patient[importColumnName] = cellValue || "";
						}
					});

					return patient;
				});

				return patients;
			})
			.catch((error) => {
				console.error("Error parsing file:", error);
				return null;
			});
	};

	// Enhanced error handling function
	const handleError = (
		error: any,
		operation: "validation" | "import" | "test"
	) => {
		console.error(`${operation} error:`, error);

		let errorMessage = "";
		let errorType: "validation" | "network" | "server" | "unknown" =
			"unknown";
		let canRetry = false;

		// Network errors
		if (
			error.code === "NETWORK_ERROR" ||
			error.message?.includes("Network Error") ||
			!error.response
		) {
			errorMessage =
				"Network connection failed. Please check your internet connection and try again.";
			errorType = "network";
			canRetry = true;
		}
		// Server errors (5xx)
		else if (error.response?.status >= 500) {
			errorMessage =
				"Server error occurred. Please try again later or contact support if the problem persists.";
			errorType = "server";
			canRetry = true;
		}
		// Validation errors (4xx)
		else if (
			error.response?.status >= 400 &&
			error.response?.status < 500
		) {
			errorMessage =
				error.response?.data?.message ||
				`Validation failed: ${error.message}`;
			errorType = "validation";
			canRetry = false;
		}
		// Other errors
		else {
			errorMessage =
				error.response?.data?.message ||
				error.message ||
				`${operation} failed. Please try again.`;
			errorType = "unknown";
			canRetry = true;
		}

		setErrorState({
			hasError: true,
			message: errorMessage,
			type: errorType,
			canRetry,
		});

		// Clear error after 5 seconds for non-critical errors
		if (errorType !== "validation") {
			setTimeout(() => {
				setErrorState((prev) => ({ ...prev, hasError: false }));
			}, 5000);
		}
	};

	// Clear error state
	const clearError = () => {
		setErrorState({
			hasError: false,
			message: "",
			type: "unknown",
			canRetry: false,
		});
	};

	// Call validate-import endpoint for testing
	const handleTestValidation = async () => {
		clearError(); // Clear any previous errors
		const importData = await prepareImportData();
		if (!importData) {
			setErrorState({
				hasError: true,
				message:
					"No data to validate. Please check your file and mapping.",
				type: "validation",
				canRetry: false,
			});
			return;
		}
		try {
			// Get current validation rows with updated values
			const updatedValidationRows = validationData.validationRows.map(
				(row) => {
					const updatedRow = { ...row };
					// Update with any user changes
					Object.keys(row).forEach((key) => {
						if (
							key !== "id" &&
							key !== "excelCell" &&
							key !== "errors" &&
							key !== "visualErrors" &&
							key !== "wasProblematic" &&
							key !== "errorMessages"
						) {
							updatedRow[key] = row[key];
						}
					});
					return updatedRow;
				}
			);

			// Merge failed rows with changed values and others together
			const mergedImportData = importData.map((dataRow, index) => {
				const validationRow = updatedValidationRows[index];
				if (validationRow) {
					// Use updated values from validation row if available
					const mergedRow = { ...dataRow };
					Object.keys(validationRow).forEach((key) => {
						if (
							key !== "id" &&
							key !== "excelCell" &&
							key !== "errors" &&
							key !== "visualErrors" &&
							key !== "wasProblematic" &&
							key !== "errorMessages"
						) {
							// Find the corresponding import column name
							const mappingRow = mappingData?.mappingRows.find(
								(mr) => mr.migranium === key
							);
							if (mappingRow) {
								const attribute = businessAttributes.find(
									(attr) => attr.key === key
								);
								const importColumnName =
									attribute?.import_column_name ||
									mappingRow.excel;
								if (importColumnName) {
									mergedRow[importColumnName] =
										validationRow[key];
								}
							}
						}
					});
					return mergedRow;
				}
				return dataRow;
			});

			console.log(
				"Test validation data:",
				JSON.stringify(mergedImportData, null, 2)
			);

			const response = await apiClient.post(
				"/api/v1/client-import/validate-import",
				mergedImportData,
				{
					headers: {
						"X-organizationId": organizationId?.toString() || "",
					},
				}
			);

			// Check if the response contains validation errors
			const validationResult = response.data;

			// Handle the wrapped response format where errors are in response.data.errors
			if (
				validationResult &&
				validationResult.success === false &&
				validationResult.errors
			) {
				const errorCount = Object.keys(validationResult.errors).length;
				// Don't show error banner for validation errors - they'll be shown in the UI
				setValidationPassed(false);
				// Update validation data with new errors
				handleBackendValidationErrors(
					validationResult.errors,
					mergedImportData
				);
			} else if (
				validationResult &&
				typeof validationResult === "object" &&
				Object.keys(validationResult).some((key) =>
					key.startsWith("row.")
				)
			) {
				const errorCount = Object.keys(validationResult).length;
				// Don't show error banner for validation errors - they'll be shown in the UI
				setValidationPassed(false);
				// Update validation data with new errors
				handleBackendValidationErrors(
					validationResult,
					mergedImportData
				);
			} else {
				// Success - clear any previous errors
				clearError();
				setValidationPassed(true);
				// Clear validation errors since validation passed
				setValidationData((prev) => ({
					...prev,
					readyToImport: mergedImportData.length,
					missingDataConflicts: 0,
					validationRows: [],
					backendValidationErrors: null,
					hasEdgeError: false,
				}));
			}
		} catch (error: any) {
			try {
				if (error.response?.data?.errors) {
					// Handle validation errors from backend
					handleBackendValidationErrors(
						error.response.data.errors,
						importData
					);
					setValidationPassed(false);
				} else {
					handleError(error, "test");
					setValidationPassed(false);
				}
			} catch (finalError: any) {
				handleError(finalError, "test");
				setValidationPassed(false);
			}
		}
	};

	const handleImport = async () => {
		clearError(); // Clear any previous errors
		setImportState((prev) => ({ ...prev, isImporting: true }));

		try {
			const importData = await prepareImportData();
			if (!importData) {
				setImportState((prev) => ({ ...prev, isImporting: false }));
				setErrorState({
					hasError: true,
					message:
						"No data to import. Please check your file and mapping.",
					type: "validation",
					canRetry: false,
				});
				return;
			}

			// Get current validation rows with updated values
			const updatedValidationRows = validationData.validationRows.map(
				(row) => {
					const updatedRow = { ...row };
					// Update with any user changes
					Object.keys(row).forEach((key) => {
						if (
							key !== "id" &&
							key !== "excelCell" &&
							key !== "errors" &&
							key !== "visualErrors" &&
							key !== "wasProblematic" &&
							key !== "errorMessages"
						) {
							updatedRow[key] = row[key];
						}
					});
					return updatedRow;
				}
			);

			// Merge failed rows with changed values and others together
			const mergedImportData = importData.map((dataRow, index) => {
				const validationRow = updatedValidationRows[index];
				if (validationRow) {
					// Use updated values from validation row if available
					const mergedRow = { ...dataRow };
					Object.keys(validationRow).forEach((key) => {
						if (
							key !== "id" &&
							key !== "excelCell" &&
							key !== "errors" &&
							key !== "visualErrors" &&
							key !== "wasProblematic" &&
							key !== "errorMessages"
						) {
							// Find the corresponding import column name
							const mappingRow = mappingData?.mappingRows.find(
								(mr) => mr.migranium === key
							);
							if (mappingRow) {
								const attribute = businessAttributes.find(
									(attr) => attr.key === key
								);
								const importColumnName =
									attribute?.import_column_name ||
									mappingRow.excel;
								if (importColumnName) {
									mergedRow[importColumnName] =
										validationRow[key];
								}
							}
						}
					});
					return mergedRow;
				}
				return dataRow;
			});

			console.log(
				"Merged import data:",
				JSON.stringify(mergedImportData, null, 2)
			);

			// Proceed with import since validation has already passed
			const response = await apiClient.post(
				"/api/v1/client-import/import-clients",
				mergedImportData,
				{
					headers: {
						"X-organizationId": organizationId?.toString() || "",
					},
				}
			);

			if (mappingData?.clearStoredCustomFields) {
				mappingData.clearStoredCustomFields();
			}

			setImportState({
				isImporting: false,
				isSuccess: true,
				importedCount: mergedImportData.length,
			});
			onImportSuccess?.(true);
		} catch (error: any) {
			setImportState((prev) => ({ ...prev, isImporting: false }));
			handleError(error, "import");
		}
	};

	// Handle backend validation errors
	const handleBackendValidationErrors = (
		validationResult: any,
		importData: any[]
	) => {
		console.log("handleBackendValidationErrors called with:", {
			validationResult,
			importDataLength: importData.length,
		});

		// Check if validationResult is an object with row.field format keys
		if (
			validationResult &&
			typeof validationResult === "object" &&
			!Array.isArray(validationResult)
		) {
			// Parse the new format where errors are returned as { "row.7.phone_number": ["error message"] }
			const rowErrors: Record<
				number,
				{ fields: string[]; messages: Record<string, string> }
			> = {};

			Object.keys(validationResult).forEach((key) => {
				// Parse keys like "row.7.phone_number" to extract row number and field
				const match = key.match(/^row\.(\d+)\.(.+)$/);
				if (match) {
					const rowIndex = parseInt(match[1]) - 1; // Convert to 0-based index
					const fieldKey = match[2]; // This is now the actual field key from backend
					const errorMessages = validationResult[key];

					if (!rowErrors[rowIndex]) {
						rowErrors[rowIndex] = { fields: [], messages: {} };
					}

					rowErrors[rowIndex].fields.push(fieldKey);
					rowErrors[rowIndex].messages[fieldKey] = Array.isArray(
						errorMessages
					)
						? errorMessages[0]
						: errorMessages;
				}
			});

			// Convert to the expected format
			const errors = Object.keys(rowErrors).map((rowIndexStr) => {
				const rowIndex = parseInt(rowIndexStr);
				return {
					row_index: rowIndex,
					fields: rowErrors[rowIndex].fields,
					messages: rowErrors[rowIndex].messages,
				};
			});

			const summary = {
				valid: importData.length - errors.length,
				invalid: errors.length,
				edge_error: false,
			};

			validationResult = { errors, summary };
		}

		const { errors, summary } = validationResult;

		// Create a map of error rows for quick lookup
		const errorRowMap = new Map();
		errors?.forEach((error: any) => {
			errorRowMap.set(error.row_index, {
				fields: error.fields || [],
				messages: error.messages || {},
			});
		});

		// Create all rows with error rows at the top
		const allRows = [];
		const errorRows = [];
		const validRows = [];

		// Process all rows from the original file data
		for (let i = 0; i < originalFileData.length; i++) {
			const originalRowData = getOriginalRowDataSync(i);
			const hasErrors = errorRowMap.has(i);

			const rowData = {
				id: i + 1,
				excelCell: `B${i + 2}`,
				...originalRowData,
				errors: hasErrors ? errorRowMap.get(i).fields : [],
				visualErrors: hasErrors ? errorRowMap.get(i).fields : [],
				wasProblematic: hasErrors,
				errorMessages: hasErrors ? errorRowMap.get(i).messages : {},
			};

			if (hasErrors) {
				errorRows.push(rowData);
			} else {
				validRows.push(rowData);
			}
		}

		// Combine error rows first, then valid rows
		const combinedRows = [...errorRows, ...validRows];

		setValidationData((prev) => ({
			...prev,
			hasEdgeError: false,
			backendValidationErrors: validationResult,
			readyToImport: summary?.valid || 0,
			missingDataConflicts: errorRows.length,
			validationRows: combinedRows,
		}));

		// Set validation as failed since there are errors
		setValidationPassed(false);
	};

	// Helper function to get original row data with field names (synchronous)
	const getOriginalRowDataSync = (rowIndex: number) => {
		if (!mappingData || !originalFileData[rowIndex]) {
			return {};
		}

		const dataRow = originalFileData[rowIndex];
		const mappedFields = mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);

		const rowData: Record<string, any> = {};

		mappedFields.forEach((mappingRow) => {
			const excelColumnIndex = mappingRow.id - 1;
			const cellValue = dataRow[excelColumnIndex];
			const fieldKey = mappingRow.migranium;

			rowData[fieldKey] = cellValue || "";
		});

		return rowData;
	};

	// Initial validation when component mounts or data changes
	useEffect(() => {
		const validateData = async () => {
			if (
				!uploadedFile ||
				!mappingData ||
				mappingData.mappingRows.length === 0 ||
				isLoadingAttributes
			) {
				return;
			}

			clearError(); // Clear any previous errors
			setIsValidating(true);
			// First, parse the file to get original data
			const fileResult = await parseFile(
				uploadedFile,
				mappingData.selectedSheet || undefined
			);
			if (fileResult.error || fileResult.data.length === 0) {
				setIsValidating(false);
				setErrorState({
					hasError: true,
					message:
						"Failed to parse file. Please check your file format.",
					type: "validation",
					canRetry: false,
				});
				return;
			}

			// Store original file data for later use
			setOriginalFileData(fileResult.data);

			const importData = await prepareImportData();
			if (!importData) {
				setIsValidating(false);
				setErrorState({
					hasError: true,
					message:
						"No data to validate. Please check your file and mapping.",
					type: "validation",
					canRetry: false,
				});
				return;
			}

			try {
				// Call backend validation immediately after sync-attributes success
				const validationResponse = await apiClient.post(
					"/api/v1/client-import/validate-import",
					importData,
					{
						headers: {
							"X-organizationId":
								organizationId?.toString() || "",
						},
					}
				);

				// Check if the response contains validation errors
				const validationResult = validationResponse.data;

				// Handle the wrapped response format where errors are in response.data.errors
				if (
					validationResult &&
					validationResult.success === false &&
					validationResult.errors
				) {
					// Handle backend validation errors
					handleBackendValidationErrors(
						validationResult.errors,
						importData
					);
					setValidationPassed(false);
				} else if (
					validationResult &&
					typeof validationResult === "object" &&
					Object.keys(validationResult).some((key) =>
						key.startsWith("row.")
					)
				) {
					// Handle direct error format
					handleBackendValidationErrors(validationResult, importData);
					setValidationPassed(false);
				} else {
					// All data is valid - show all rows
					const allRows: ValidationRow[] = [];
					for (let i = 0; i < originalFileData.length; i++) {
						const originalRowData = getOriginalRowDataSync(i);
						allRows.push({
							id: i + 1,
							excelCell: `B${i + 2}`,
							...originalRowData,
							errors: [],
							visualErrors: [],
							wasProblematic: false,
							errorMessages: {},
						});
					}

					setValidationData((prev) => ({
						...prev,
						readyToImport: importData.length,
						missingDataConflicts: 0,
						validationRows: allRows,
						backendValidationErrors: null,
						hasEdgeError: false,
					}));
					setValidationPassed(true);
				}
			} catch (error: any) {
				try {
					if (error.response?.data?.errors) {
						// Handle validation errors from backend
						handleBackendValidationErrors(
							error.response.data.errors,
							importData
						);
						setValidationPassed(false);
					} else {
						handleError(error, "validation");
						// Fallback to client-side validation if backend fails
						performClientSideValidation();
					}
				} catch (finalError: any) {
					handleError(finalError, "validation");
					// Fallback to client-side validation if backend fails
					performClientSideValidation();
				}
			} finally {
				setIsValidating(false);
			}
		};

		// Fallback client-side validation
		const performClientSideValidation = async () => {
			if (!uploadedFile || !mappingData) {
				return;
			}

			try {
				const fileResult = await parseFile(
					uploadedFile,
					mappingData.selectedSheet || undefined
				);
				if (fileResult.error || fileResult.data.length === 0) {
					return;
				}

				const validationRows: ValidationRow[] = [];
				let missingDataCount = 0;
				let readyToImportCount = 0;

				const mappedFields = mappingData.mappingRows.filter(
					(row) => row.migranium && row.migranium.trim() !== ""
				);
				const requiredFields = getRequiredFields();

				fileResult.data.forEach((dataRow, index) => {
					const validationRow: ValidationRow = {
						id: index + 1,
						excelCell: `B${index + 2}`,
						errors: [],
						visualErrors: [],
						wasProblematic: false,
					};

					let hasErrors = false;

					mappedFields.forEach((mappingRow) => {
						const excelColumnIndex = mappingRow.id - 1;
						const cellValue = dataRow[excelColumnIndex];
						const fieldKey = mappingRow.migranium;
						const fieldType = getFieldValidationType(fieldKey);

						validationRow[fieldKey] = cellValue || "";

						if (
							requiredFields.includes(fieldKey) &&
							(!cellValue || cellValue.toString().trim() === "")
						) {
							validationRow.errors.push(fieldKey);
							validationRow.visualErrors.push(fieldKey);
							hasErrors = true;
						}

						if (cellValue && cellValue.toString().trim() !== "") {
							let isValidFormat = true;
							const cellValueStr = cellValue.toString().trim();

							switch (fieldType) {
								case "email":
									isValidFormat = validateEmail(cellValueStr);
									break;
								case "phone":
									isValidFormat =
										validatePhoneNumber(cellValueStr);
									break;
								case "date":
									isValidFormat = validateDate(cellValueStr);
									break;
								case "number":
									isValidFormat =
										validateNumber(cellValueStr);
									break;
								case "boolean":
									isValidFormat =
										validateBoolean(cellValueStr);
									break;
								default:
									isValidFormat = true;
									break;
							}

							if (!isValidFormat) {
								validationRow.errors.push(fieldKey);
								validationRow.visualErrors.push(fieldKey);
								hasErrors = true;
							}
						}
					});

					if (hasErrors) {
						validationRow.wasProblematic = true;
					}

					validationRows.push(validationRow);

					if (hasErrors) {
						missingDataCount++;
					} else {
						readyToImportCount++;
					}
				});

				setValidationData({
					readyToImport: readyToImportCount,
					missingDataConflicts: missingDataCount,
					duplicateDataConflicts: 0,
					showMissingData: missingDataCount > 0,
					showDuplicateData: false,
					validationRows: validationRows,
					discardedMissingDataRows: new Set(),
					discardedDuplicateDataRows: new Set(),
					backendValidationErrors: null,
					hasEdgeError: false,
				});
			} catch (error) {
				console.error("Error in client-side validation:", error);
			}
		};

		validateData();
	}, [
		uploadedFile,
		mappingData,
		businessAttributes,
		isLoadingAttributes,
		organizationId,
	]);

	const missingDataRows = validationData.validationRows.filter(
		(row) => row.wasProblematic
	);

	const getMappedFields = () => {
		if (!mappingData) return [];
		return mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);
	};

	const mappedFields = getMappedFields();

	const handleFieldUpdate = (
		rowId: number,
		fieldKey: string,
		newValue: string
	) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.map((row) => {
				if (row.id === rowId) {
					const updatedRow = { ...row, [fieldKey]: newValue };
					const requiredFields = getRequiredFields();
					let newVisualErrors = [...row.visualErrors];

					// Remove the field from visual errors since user is updating it
					newVisualErrors = newVisualErrors.filter(
						(error) => error !== fieldKey
					);

					let newErrors: string[] = [];
					let newErrorMessages = { ...row.errorMessages };

					// Remove error message for this field since user is updating it
					if (newErrorMessages[fieldKey]) {
						delete newErrorMessages[fieldKey];
					}

					const mappedFields =
						mappingData?.mappingRows.filter(
							(mappingRow) =>
								mappingRow.migranium &&
								mappingRow.migranium.trim() !== ""
						) || [];

					mappedFields.forEach((mappingRow) => {
						const fieldName = mappingRow.migranium;
						const fieldType = getFieldValidationType(fieldName);
						const fieldValue =
							fieldName === fieldKey
								? newValue
								: updatedRow[fieldName] || "";

						if (
							requiredFields.includes(fieldName) &&
							(!fieldValue || fieldValue.toString().trim() === "")
						) {
							newErrors.push(fieldName);
							if (fieldName !== fieldKey) {
								newVisualErrors.push(fieldName);
							}
						}

						if (fieldValue && fieldValue.toString().trim() !== "") {
							let isValidFormat = true;
							const fieldValueStr = fieldValue.toString().trim();

							switch (fieldType) {
								case "email":
									isValidFormat =
										validateEmail(fieldValueStr);
									break;
								case "phone":
									isValidFormat =
										validatePhoneNumber(fieldValueStr);
									break;
								case "date":
									isValidFormat = validateDate(fieldValueStr);
									break;
								case "number":
									isValidFormat =
										validateNumber(fieldValueStr);
									break;
								case "boolean":
									isValidFormat =
										validateBoolean(fieldValueStr);
									break;
								default:
									isValidFormat = true;
									break;
							}

							if (!isValidFormat) {
								newErrors.push(fieldName);
								if (fieldName !== fieldKey) {
									newVisualErrors.push(fieldName);
								}
							}
						}
					});

					return {
						...updatedRow,
						errors: newErrors,
						visualErrors: newVisualErrors,
						wasProblematic: newErrors.length > 0,
						errorMessages: newErrorMessages,
					};
				}
				return row;
			}),
		}));
	};

	const handleDeleteRow = (rowId: number) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.filter(
				(row) => row.id !== rowId
			),
		}));
	};

	// Get the field label for display
	const getFieldLabel = (fieldKey: string): string => {
		const attribute = businessAttributes.find(
			(attr) => attr.key === fieldKey
		);
		return attribute?.label || fieldKey;
	};

	// Get the import column name for the field - now just return the key
	const getImportColumnName = (fieldKey: string): string => {
		// Since we're using keys consistently, just return the key
		return fieldKey;
	};

	// Get error message for a specific field
	const getFieldErrorMessage = (
		row: ValidationRow,
		fieldKey: string
	): string => {
		if (row.errorMessages && row.errorMessages[fieldKey]) {
			return row.errorMessages[fieldKey];
		}

		// Fallback to generic error messages
		if (row.errors.includes(fieldKey)) {
			const fieldType = getFieldValidationType(fieldKey);
			switch (fieldType) {
				case "email":
					return "Invalid email format";
				case "phone":
					return "Invalid phone number format";
				case "date":
					return "Invalid date format";
				case "number":
					return "Invalid number format";
				case "boolean":
					return "Invalid boolean value";
				default:
					return "This field is required";
			}
		}
		return "";
	};

	if (importState.isSuccess) {
		return (
			<ImportSuccessCard
				patientsCount={importState.importedCount}
				onImportAgain={onImportAgain || (() => {})}
				onDone={onDone || (() => {})}
			/>
		);
	}

	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{/* Error Banner */}
			{errorState.hasError && (
				<div
					className={`w-full rounded-lg border p-4 ${
						errorState.type === "validation"
							? "border-red-200 bg-red-50"
							: errorState.type === "network"
								? "border-yellow-200 bg-yellow-50"
								: "border-orange-200 bg-orange-50"
					}`}
				>
					<div className="flex items-start gap-3">
						<AlertCircle
							className={`mt-0.5 h-5 w-5 ${
								errorState.type === "validation"
									? "text-red-500"
									: errorState.type === "network"
										? "text-yellow-500"
										: "text-orange-500"
							}`}
						/>
						<div className="flex-1">
							<div
								className={`text-sm font-medium ${
									errorState.type === "validation"
										? "text-red-800"
										: errorState.type === "network"
											? "text-yellow-800"
											: "text-orange-800"
								}`}
							>
								{errorState.type === "validation"
									? "Validation Error"
									: errorState.type === "network"
										? "Network Error"
										: "Error"}
							</div>
							<div
								className={`text-sm ${
									errorState.type === "validation"
										? "text-red-700"
										: errorState.type === "network"
											? "text-yellow-700"
											: "text-orange-700"
								}`}
							>
								{errorState.message}
							</div>
						</div>
						<div className="flex gap-2">
							{errorState.canRetry && (
								<button
									onClick={() => {
										clearError();
										if (validationPassed) {
											handleImport();
										} else {
											handleTestValidation();
										}
									}}
									className="flex items-center gap-1 rounded px-2 py-1 text-xs font-medium text-gray-700 hover:bg-gray-100"
								>
									<RefreshCw className="h-3 w-3" />
									Retry
								</button>
							)}
							<button
								onClick={clearError}
								className="text-gray-400 hover:text-gray-600"
							>
								×
							</button>
						</div>
					</div>
				</div>
			)}

			<div className="flex w-full flex-col items-start justify-start gap-4">
				<SelectSheetInfoCard
					onNext={onPopulateData || (() => {})}
					selectedSheet={
						mappingData?.selectedSheet || "No sheet selected"
					}
					sheets={mappingData?.availableSheets || []}
					buttonText="Populate data"
				/>

				<div className="flex w-full flex-col items-start justify-start gap-4">
					{isValidating ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-blue-600">
									Validating data...
								</div>
								<div className="text-xs text-gray-500">
									Please wait while we validate your data.
								</div>
							</div>
						</div>
					) : validationPassed && validationData.readyToImport > 0 ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="flex items-center gap-2 text-sm font-medium text-green-600">
									<CheckCircle className="h-4 w-4" />
									{validationData.readyToImport} data is ready
									to import
								</div>
								<div className="text-xs text-gray-500">
									These data is perfectly fine to import in
									system.
								</div>
							</div>
						</div>
					) : !validationPassed &&
					  validationData.missingDataConflicts > 0 ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-red-600">
									{validationData.missingDataConflicts}{" "}
									validation errors found
								</div>
								<div className="text-xs text-gray-500">
									Please fix the errors in the rows below
									before importing.
								</div>
							</div>
						</div>
					) : !validationPassed &&
					  validationData.missingDataConflicts === 0 ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-orange-600">
									Validation required
								</div>
								<div className="text-xs text-gray-500">
									Click "Test" to validate your data before
									importing.
								</div>
							</div>
						</div>
					) : null}
					{validationData.validationRows.length > 0 && (
						<div className="flex w-full flex-col items-start justify-start gap-3 rounded-lg bg-white p-4">
							<div className="flex w-full items-center justify-between border-b border-gray-200 pb-3">
								<div className="flex flex-1 flex-col items-start justify-start gap-1">
									<div
										className={`text-sm font-medium ${
											validationData.missingDataConflicts >
											0
												? "text-red-600"
												: "text-green-600"
										}`}
									>
										All Data (
										{validationData.validationRows.length}{" "}
										rows)
									</div>
									<div className="text-xs text-gray-500">
										{validationData.missingDataConflicts > 0
											? `${validationData.missingDataConflicts} rows have validation errors. Error rows are shown at the top.`
											: "All data is valid and ready for import."}
									</div>
									{validationData.hasEdgeError && (
										<div className="text-xs font-medium text-orange-600">
											⚠️ Edge case detected: All rows need
											to be updated
										</div>
									)}
								</div>
								<div className="flex items-center gap-2">
									<button
										onClick={() => {
											const missingDataRowIds =
												validationData.validationRows
													.filter(
														(row) =>
															row.errors.length >
															0
													)
													.map((row) => row.id);

											setValidationData((prev) => ({
												...prev,
												showMissingData: false,
												discardedMissingDataRows:
													new Set(missingDataRowIds),
											}));
										}}
										className="flex h-9 items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-50"
									>
										<Trash2 className="h-3 w-3" />
										Discard conflicted data
									</button>
									<button
										onClick={() =>
											setValidationData((prev) => ({
												...prev,
												showMissingData:
													!prev.showMissingData,

												discardedMissingDataRows:
													prev.showMissingData
														? prev.discardedMissingDataRows
														: new Set(),
											}))
										}
										className={`flex h-9 w-9 items-center justify-center rounded-md transition-transform hover:bg-gray-100 ${
											validationData.showMissingData
												? "rotate-180"
												: ""
										}`}
									>
										<ChevronDown className="h-3 w-3" />
									</button>
								</div>
							</div>

							{validationData.showMissingData && (
								<div className="w-full overflow-x-auto">
									<table className="w-full border-collapse">
										<thead>
											<tr className="border-b border-gray-200">
												<th className="w-12 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														#
													</div>
												</th>
												<th className="w-20 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														Excel Cell
													</div>
												</th>
												{mappedFields.map((field) => {
													const isRequired =
														getRequiredFields().includes(
															field.migranium
														);
													return (
														<th
															key={field.id}
															className="min-w-[150px] px-2 py-3 text-left"
														>
															<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
																{getFieldLabel(
																	field.migranium
																)}{" "}
																{isRequired
																	? "*"
																	: ""}
															</div>
														</th>
													);
												})}
												<th className="w-12 px-2 py-3 text-center">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500"></div>
												</th>
											</tr>
										</thead>
										<tbody>
											{validationData.validationRows.map(
												(row) => (
													<tr
														key={row.id}
														className={`border-t border-gray-200 ${
															row.wasProblematic
																? "bg-red-50"
																: ""
														}`}
													>
														<td className="w-12 px-2 py-3">
															<div className="font-['Inter'] text-sm leading-tight font-medium text-gray-900">
																{row.id}
															</div>
														</td>
														<td className="w-20 px-2 py-3">
															<div className="font-['Inter'] text-sm leading-tight font-normal text-gray-500">
																{row.excelCell}
															</div>
														</td>
														{mappedFields.map(
															(field) => {
																const hasVisualError =
																	row.visualErrors.includes(
																		field.migranium
																	);
																const fieldValue =
																	row[
																		field
																			.migranium
																	] || "";
																const errorMessage =
																	getFieldErrorMessage(
																		row,
																		field.migranium
																	);

																return (
																	<td
																		key={
																			field.id
																		}
																		className="min-w-[150px] px-2 py-3"
																	>
																		<div className="relative">
																			<input
																				type="text"
																				value={
																					fieldValue
																				}
																				onChange={(
																					e
																				) =>
																					handleFieldUpdate(
																						row.id,
																						field.migranium,
																						e
																							.target
																							.value
																					)
																				}
																				className={`h-9 w-full rounded-md border px-3 py-2 text-xs focus:ring-2 focus:ring-blue-500 focus:outline-none ${
																					hasVisualError
																						? "border-red-500 bg-red-50 text-red-500"
																						: "border-gray-300 text-gray-900"
																				}`}
																				placeholder={`Enter ${getFieldLabel(field.migranium).toLowerCase()}`}
																			/>
																			{errorMessage && (
																				<div className="absolute -bottom-5 left-0 max-w-[200px] text-xs text-red-500">
																					{
																						errorMessage
																					}
																				</div>
																			)}
																		</div>
																	</td>
																);
															}
														)}
														<td className="w-12 px-2 py-3 text-center">
															<button
																onClick={() =>
																	handleDeleteRow(
																		row.id
																	)
																}
																className="flex h-6 w-6 items-center justify-center text-gray-500 transition-colors hover:text-red-500"
															>
																<MinusCircle className="h-4 w-4" />
															</button>
														</td>
													</tr>
												)
											)}
										</tbody>
									</table>
								</div>
							)}
						</div>
					)}
				</div>

				<div className="mt-auto flex w-full items-center justify-between gap-3 bg-white px-6 py-4">
					<button
						onClick={onBack}
						className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-100"
					>
						Back
					</button>
					<div className="flex items-center gap-3">
						<button
							onClick={handleTestValidation}
							disabled={isValidating || importState.isImporting}
							className="flex h-9 items-center justify-center gap-2 rounded-md border border-[#005893] bg-white px-4 py-2 text-xs font-medium text-[#005893] hover:bg-blue-50 disabled:cursor-not-allowed disabled:opacity-50"
						>
							{isValidating ? (
								<>
									<RefreshCw className="h-3 w-3 animate-spin" />
									Testing...
								</>
							) : (
								"Test"
							)}
						</button>
						<button
							onClick={handleImport}
							disabled={
								importState.isImporting ||
								!validationPassed ||
								isValidating
							}
							className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 text-xs font-medium text-white hover:bg-[#004a7a] disabled:cursor-not-allowed disabled:opacity-50"
						>
							{importState.isImporting ? (
								<>
									<RefreshCw className="h-3 w-3 animate-spin" />
									Importing...
								</>
							) : (
								`Import ${validationData.readyToImport} ${validationData.readyToImport === 1 ? "Record" : "Records"}`
							)}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
