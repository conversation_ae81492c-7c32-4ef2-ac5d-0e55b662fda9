// == attributes ==
export interface AttributeResponse {
	id: number;
	key: string;
	migranium_label: string | null;
	is_required?: boolean;
	is_validator?: boolean;
	is_system_field?: boolean;
	import_column_name: string;
	type: string;
	options?: string[];
}

export interface AttributeExcelMap {
	attribute_id: number;
	import_column_name: string;
	is_validator: boolean;
}

export interface NewAttribute {
	label: string;
	is_validator: boolean;
	import_column_name: string;
	type: string;
	options?: string[];
}

export interface AttributeMapRequest {
	existing_attributes: AttributeExcelMap[];
	new_attributes: NewAttribute[];
}

// == validation ==

export interface FieldError {
	field: string;
	messages: string[];
}
export interface RowError {
	row: number;
	field?: string;
	messages: string[];
}

export interface ImportClientValidationError {
	errors: RowError[];
	success: boolean;
}

export interface RowCell {
	[key: string]: string;
}

export interface ImportClientRow {
	row: number;
	cells: RowCell;
	errors: FieldError[];
	generalErrors: string[];
}
