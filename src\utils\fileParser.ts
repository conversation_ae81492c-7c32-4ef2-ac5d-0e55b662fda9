import <PERSON> from "papaparse";
import * as XLSX from "xlsx";

export interface FileParseResult {
	headers: string[];
	data: any[][];
	error?: string;
}

export interface ExcelSheetsResult {
	sheets: string[];
	error?: string;
}

export const parseCSVFile = (file: File): Promise<FileParseResult> => {
	return new Promise((resolve) => {
		Papa.parse(file, {
			header: false,
			skipEmptyLines: true,
			complete: (results) => {
				if (results.errors.length > 0) {
					resolve({
						headers: [],
						data: [],
						error: results.errors[0].message,
					});
					return;
				}

				const data = results.data as string[][];
				if (data.length === 0) {
					resolve({
						headers: [],
						data: [],
						error: "File is empty",
					});
					return;
				}

				const headers = data[0].filter(
					(header) => header && header.trim() !== ""
				);
				const dataRows = data.slice(1);

				resolve({
					headers,
					data: dataRows,
				});
			},
			error: (error) => {
				resolve({
					headers: [],
					data: [],
					error: error.message,
				});
			},
		});
	});
};

export const getExcelSheets = (file: File): Promise<ExcelSheetsResult> => {
	return new Promise((resolve) => {
		const reader = new FileReader();

		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: "array" });

				if (workbook.SheetNames.length === 0) {
					resolve({
						sheets: [],
						error: "No worksheets found in the file",
					});
					return;
				}

				resolve({
					sheets: workbook.SheetNames,
				});
			} catch (error) {
				resolve({
					sheets: [],
					error:
						error instanceof Error
							? error.message
							: "Failed to read Excel file",
				});
			}
		};

		reader.onerror = () => {
			resolve({
				sheets: [],
				error: "Failed to read file",
			});
		};

		reader.readAsArrayBuffer(file);
	});
};

export const parseExcelFile = (
	file: File,
	sheetName?: string
): Promise<FileParseResult> => {
	return new Promise((resolve) => {
		const reader = new FileReader();

		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: "array" });

				const targetSheetName = sheetName || workbook.SheetNames[0];
				if (!targetSheetName || !workbook.Sheets[targetSheetName]) {
					resolve({
						headers: [],
						data: [],
						error: sheetName
							? `Sheet "${sheetName}" not found in the file`
							: "No worksheets found in the file",
					});
					return;
				}

				const worksheet = workbook.Sheets[targetSheetName];

				const jsonData = XLSX.utils.sheet_to_json(worksheet, {
					header: 1,
					defval: "",
				}) as string[][];

				if (jsonData.length === 0) {
					resolve({
						headers: [],
						data: [],
						error: "Worksheet is empty",
					});
					return;
				}

				const headers = jsonData[0].filter(
					(header) => header && header.toString().trim() !== ""
				);
				const dataRows = jsonData.slice(1);

				resolve({
					headers,
					data: dataRows,
				});
			} catch (error) {
				resolve({
					headers: [],
					data: [],
					error:
						error instanceof Error
							? error.message
							: "Failed to parse Excel file",
				});
			}
		};

		reader.onerror = () => {
			resolve({
				headers: [],
				data: [],
				error: "Failed to read file",
			});
		};

		reader.readAsArrayBuffer(file);
	});
};

export const parseFile = async (
	file: File,
	sheetName?: string
): Promise<FileParseResult> => {
	const fileName = file.name.toLowerCase();

	if (fileName.endsWith(".csv")) {
		return parseCSVFile(file);
	} else if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
		return parseExcelFile(file, sheetName);
	} else {
		return {
			headers: [],
			data: [],
			error: "Unsupported file format. Please upload a CSV or Excel file.",
		};
	}
};

export const getFileHeaders = async (
	file: File,
	sheetName?: string
): Promise<string[]> => {
	const result = await parseFile(file, sheetName);
	if (result.error) {
		console.error("Error parsing file:", result.error);
		return [];
	}
	return result.headers;
};

// To get all sheets available
export const getFileSheets = async (file: File): Promise<string[]> => {
	const fileName = file.name.toLowerCase();

	if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
		const result = await getExcelSheets(file);
		if (result.error) {
			console.error("Error getting sheets:", result.error);
			return [];
		}
		return result.sheets;
	}
	return [];
};

const calculateSimilarity = (str1: string, str2: string): number => {
	const s1 = str1.toLowerCase().trim();
	const s2 = str2.toLowerCase().trim();

	if (s1 === s2) return 1;

	const len1 = s1.length;
	const len2 = s2.length;

	if (len1 === 0) return len2 === 0 ? 1 : 0;
	if (len2 === 0) return 0;

	const matrix = Array(len2 + 1)
		.fill(null)
		.map(() => Array(len1 + 1).fill(null));

	for (let i = 0; i <= len1; i++) matrix[0][i] = i;
	for (let j = 0; j <= len2; j++) matrix[j][0] = j;

	for (let j = 1; j <= len2; j++) {
		for (let i = 1; i <= len1; i++) {
			const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
			matrix[j][i] = Math.min(
				matrix[j - 1][i] + 1, // deletion
				matrix[j][i - 1] + 1, // insertion
				matrix[j - 1][i - 1] + cost // substitution
			);
		}
	}

	const maxLen = Math.max(len1, len2);
	return (maxLen - matrix[len2][len1]) / maxLen;
};

const matchesField = (
	csvHeader: string,
	fieldKey: string,
	fieldLabel: string
): number => {
	// Add null/undefined checks to prevent errors
	if (!csvHeader || !fieldKey || !fieldLabel) {
		return 0;
	}

	const header = csvHeader.toLowerCase().trim();
	const key = fieldKey.toLowerCase();
	const label = fieldLabel.toLowerCase();

	if (header === key || header === label) return 1;

	const variations: Record<string, string[]> = {
		firstname: ["first name", "fname", "given name", "forename"],
		lastname: ["last name", "lname", "surname", "family name"],
		email: ["email address", "e-mail", "mail", "email_address"],
		phone: [
			"phone number",
			"telephone",
			"mobile",
			"cell",
			"contact",
			"phone_number",
		],
	};

	const fieldVariations = variations[key] || [];
	for (const variation of fieldVariations) {
		if (
			header === variation ||
			header.includes(variation) ||
			variation.includes(header)
		) {
			return 0.9;
		}
	}
	if (header.includes(key) || key.includes(header)) return 0.8;
	if (header.includes(label) || label.includes(header)) return 0.8;

	const keySimilarity = calculateSimilarity(header, key);
	const labelSimilarity = calculateSimilarity(header, label);
	const maxSimilarity = Math.max(keySimilarity, labelSimilarity);

	return maxSimilarity > 0.6 ? maxSimilarity : 0;
};

export interface FieldOption {
	key: string;
	label: string;
	type: string;
}

export interface AutoMatchResult {
	csvHeader: string;
	matchedField: FieldOption | null;
	confidence: number;
}

export const autoMatchColumns = (
	csvHeaders: string[],
	availableFields: FieldOption[]
): AutoMatchResult[] => {
	const results: AutoMatchResult[] = [];
	const usedFields = new Set<string>();

	for (const header of csvHeaders) {
		let bestMatch: FieldOption | null = null;
		let bestScore = 0;

		for (const field of availableFields) {
			if (usedFields.has(field.key)) continue;

			const score = matchesField(header, field.key, field.label);
			if (score > bestScore && score > 0.7) {
				bestMatch = field;
				bestScore = score;
			}
		}

		if (bestMatch) {
			usedFields.add(bestMatch.key);
		}

		results.push({
			csvHeader: header,
			matchedField: bestMatch,
			confidence: bestScore,
		});
	}

	for (const result of results) {
		if (result.matchedField) continue;

		let bestMatch: FieldOption | null = null;
		let bestScore = 0;

		for (const field of availableFields) {
			if (usedFields.has(field.key)) continue;

			const score = matchesField(
				result.csvHeader,
				field.key,
				field.label
			);
			if (score > bestScore && score > 0.5) {
				bestMatch = field;
				bestScore = score;
			}
		}

		if (bestMatch) {
			usedFields.add(bestMatch.key);
			result.matchedField = bestMatch;
			result.confidence = bestScore;
		}
	}

	return results;
};
